/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    "./frontend/index.html",
    "./frontend/src/**/*.{vue,js,ts,jsx,tsx}",
    "./admin/index.html",
    "./admin/src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
  // 確保 Tailwind 可以處理 Vue 檔案中的 @apply 指令
  corePlugins: {
    preflight: true,
  },
  important: false, // 暫時關閉 important，讓內聯樣式能夠正常工作
}
