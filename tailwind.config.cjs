/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    "./frontend/index.html",
    "./frontend/src/**/*.{vue,js,ts,jsx,tsx}",
    "./admin/index.html",
    "./admin/src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
  // 確保 Tailwind 可以處理 Vue 檔案中的 @apply 指令
  corePlugins: {
    preflight: true,
  },
  important: '#app', // 限制 important 範圍，不影響富文本內聯樣式
}
