cd<script setup>
import { ref, onMounted, onUnmounted, reactive, computed, watch, nextTick } from 'vue'
import imgPath from '../assets/product/59321971.webp'
import imgPath2 from '../assets/product/59321985.webp'
import { useRoute, useRouter } from 'vue-router'
const { VITE_API_URL } = import.meta.env

import { Swiper, SwiperSlide } from 'swiper/vue'
import api from '../utils/api.js'
import { storeToRefs } from 'pinia'
import piniaStores from '../stores/apiStore'
import TwDoptions from '../components/utils/TwDoptions.vue'
import { getImageUrl } from '../config/apiConfig'

const handelPinia = piniaStores()
const { isUser } = storeToRefs(handelPinia)
const optionsData = reactive(['商品說明', '商品規格'])
const nowOptions = ref(optionsData[0])
const { postApiData, getApiData, delApiData, cancelRequests, getProductById } = api()
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'
// import 'swiper/css/autoplay';

import { Pagination, Navigation, Autoplay } from 'swiper/modules'
import { message, Modal } from 'ant-design-vue'
const route = useRoute()
const keyWord = route.query

const modules = [Pagination, Navigation, Autoplay]
// const productData =
// {
//   name: '沒麥乾拌糆-綠椰咖',
//   img: [
//     imgPath, imgPath2
//   ],
// }

const productData = ref(null)
const router = useRouter()

// 新增：載入狀態管理
const loading = ref(true)
const error = ref(null)
const loadingText = ref('載入商品資訊...')

// 新增：儲存規格選項
const specTypes = ref([])
const specCombinations = ref([])
const selectedSpecs = reactive({})

// 新增：圖片相關
const currentImageIndex = ref(0)
const swiperRef = ref(null)

// 新增：回到頂部功能
const showBackToTop = ref(false)


onMounted(async () => {
  // 確保頁面滾動到頂部 - 使用多種方法確保可靠性
  await nextTick()
  
  // 方法1: 立即強制滾動
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0
  
  // 方法2: 使用 window.scrollTo
  window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
  
  // 方法3: 延遲滾動確保DOM已更新
  setTimeout(() => {
    window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
  }, 0)
  
  // 新增：添加滾動監聽
  window.addEventListener('scroll', handleScroll)
  document.addEventListener('scroll', handleScroll)
  document.documentElement.addEventListener('scroll', handleScroll)
  document.body.addEventListener('scroll', handleScroll)
  
  // 延遲添加滾動監聽器，確保 DOM 完全載入
  setTimeout(() => {
    window.addEventListener('scroll', handleScroll)
    document.addEventListener('scroll', handleScroll)
    document.documentElement.addEventListener('scroll', handleScroll)
    document.body.addEventListener('scroll', handleScroll)
  }, 1000)
  
  try {
    loading.value = true
    error.value = null
    
    // 檢查商品ID是否存在
    if (!route.query.id) {
      throw new Error('商品ID未提供')
    }
    
    loadingText.value = '正在載入商品資訊...'
    
    // 直接使用專用的getProductById函數
    const res = await getProductById(route.query.id)
    
    if (!res || !res.success || !res.product) {
      throw new Error('找不到此商品')
    }
    
    const product = res.product
    productData.value = product
    
    // 解析規格
    loadingText.value = '載入商品規格...'
    if (product.specTypes) {
      try {
        specTypes.value = JSON.parse(product.specTypes)
        
        // 初始化選擇的規格
        specTypes.value.forEach(type => {
          selectedSpecs[type.name] = null
        })
      } catch (e) {
        console.error('規格類型解析失敗:', e)
        specTypes.value = []
      }
    }
    
    if (product.specCombinations) {
      try {
        specCombinations.value = JSON.parse(product.specCombinations)
      } catch (e) {
        console.error('規格組合解析失敗:', e)
        specCombinations.value = []
      }
    }
    
    // 數據載入完成後再次確保頁面在頂部
    await nextTick()
    window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
    
    // 修正重點商品資訊的內聯樣式
    await nextTick()
    fixHighlightsStyles()

    // 額外的強制顏色修正
    setTimeout(() => {
      forceInlineStyles() // 新增：強制應用內聯樣式
      fixHighlightsStyles()
      forceColorStyles()
    }, 100)

    // 檢測裝置類型
    checkMobileDevice()

    // 監聽視窗大小變化
    window.addEventListener('resize', checkMobileDevice)

  } catch (err) {
    console.error('💥 載入商品失敗:', err)
    error.value = err.message || '載入商品失敗，請稍後再試'
  } finally {
    loading.value = false
  }
})

// 新增：組件卸載時清理監聽器
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  document.removeEventListener('scroll', handleScroll)
  document.documentElement.removeEventListener('scroll', handleScroll)
  document.body.removeEventListener('scroll', handleScroll)
  window.removeEventListener('resize', checkMobileDevice)
})

// 處理重點商品資訊的 HTML，確保樣式正確顯示
const processHighlightsHtml = (html) => {
  if (!html) return ''
  
  // 建立一個臨時的 DOM 元素來處理 HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html
  
  // 找出所有有 style 屬性的元素
  const styledElements = tempDiv.querySelectorAll('[style]')
  
  styledElements.forEach(element => {
    const style = element.getAttribute('style')
    if (!style) return
    
    // 建立新的樣式字串
    let newStyle = style
    
    // 處理顏色轉換
    if (style.includes('color')) {
      // HSL 轉 RGB 的對應表
      const colorMap = {
        'hsl(0, 75%, 60%)': 'rgb(255, 0, 0)',
        'hsl(0,75%,60%)': 'rgb(255, 0, 0)',
        'hsl(180, 75%, 60%)': 'rgb(0, 255, 255)', // 青色
        'hsl(180,75%,60%)': 'rgb(0, 255, 255)', // 青色
        'hsl(194, 70%, 21%)': 'rgb(16, 74, 90)',
        'hsl(194,70%,21%)': 'rgb(16, 74, 90)',
        'hsl(216, 5%, 32%)': 'rgb(77, 81, 86)',
        'hsl(216,5%,32%)': 'rgb(77, 81, 86)'
      }
      
      // 替換所有匹配的顏色
      Object.entries(colorMap).forEach(([hsl, rgb]) => {
        if (newStyle.includes(hsl)) {
          newStyle = newStyle.replace(new RegExp(hsl, 'g'), rgb)
        }
      })
      
      // 處理可能的其他 HSL 格式
      newStyle = newStyle.replace(/hsl\(0,\s*75%,\s*60%\)/g, 'rgb(255, 0, 0)')
      newStyle = newStyle.replace(/hsl\(180,\s*75%,\s*60%\)/g, 'rgb(0, 255, 255)')
      newStyle = newStyle.replace(/hsl\(194,\s*70%,\s*21%\)/g, 'rgb(16, 74, 90)')
      newStyle = newStyle.replace(/hsl\(216,\s*5%,\s*32%\)/g, 'rgb(77, 81, 86)')
    }
    
    // 更新元素的 style 屬性
    element.setAttribute('style', newStyle)
    
    // 額外添加 data 屬性來強制顏色
    if (newStyle.includes('rgb(255, 0, 0)')) {
      element.setAttribute('data-color', 'red')
    } else if (newStyle.includes('rgb(0, 255, 255)')) {
      element.setAttribute('data-color', 'cyan')
    } else if (newStyle.includes('rgb(16, 74, 90)')) {
      element.setAttribute('data-color', 'blue')
    } else if (newStyle.includes('rgb(77, 81, 86)')) {
      element.setAttribute('data-color', 'gray')
    }
  })
  
  return tempDiv.innerHTML
}

// 強制應用內聯樣式的函數 - 覆蓋 Tailwind 的 important 設定
const forceInlineStyles = () => {
  // 處理重點商品資訊區域
  const highlightsContainer = document.querySelector('.highlights-content')
  if (highlightsContainer) {
    const allElements = highlightsContainer.querySelectorAll('*[style]')
    allElements.forEach(element => {
      const style = element.getAttribute('style')
      if (!style) return

      // 解析並強制應用每個樣式屬性
      const styleProps = style.split(';').filter(prop => prop.trim())
      styleProps.forEach(prop => {
        const [property, value] = prop.split(':').map(s => s.trim())
        if (property && value) {
          element.style.setProperty(property, value, 'important')
        }
      })
    })
  }

  // 處理所有富文本內容區域
  const richTextContainers = document.querySelectorAll('.rich-text-content')
  richTextContainers.forEach(container => {
    const elementsWithStyle = container.querySelectorAll('*[style]')
    elementsWithStyle.forEach(element => {
      const style = element.getAttribute('style')
      if (!style) return

      // 強制應用每個內聯樣式屬性
      const styleProps = style.split(';').filter(prop => prop.trim())
      styleProps.forEach(prop => {
        const [property, value] = prop.split(':').map(s => s.trim())
        if (property && value) {
          element.style.setProperty(property, value, 'important')
        }
      })
    })
  })
}

// 修正重點商品資訊的內聯樣式（保留作為備用）
const fixHighlightsStyles = () => {
  // 這個函數現在主要用於確保樣式在 DOM 更新後仍然正確
  const highlightsContainer = document.querySelector('.highlights-content')
  if (!highlightsContainer) return

  // 找出所有有內聯顏色樣式的元素並強制設定
  const allElements = highlightsContainer.querySelectorAll('*[style*="color"]')

  allElements.forEach(element => {
    const style = element.getAttribute('style')
    if (!style) return

    // 檢查並強制設定特定顏色
    if (style.includes('hsl(120, 75%, 60%)')) {
      element.style.setProperty('color', 'hsl(120, 75%, 60%)', 'important')
      element.classList.add('highlight-green')
    } else if (style.includes('hsl(240, 75%, 60%)')) {
      element.style.setProperty('color', 'hsl(240, 75%, 60%)', 'important')
      element.classList.add('highlight-blue')
    } else if (style.includes('rgb(255, 0, 0)') || style.includes('rgb(255,0,0)')) {
      element.style.setProperty('color', 'rgb(255, 0, 0)', 'important')
      element.classList.add('highlight-red')
    }
  })

  // 找出所有有 data-color 屬性的元素
  const coloredElements = highlightsContainer.querySelectorAll('[data-color]')

  coloredElements.forEach(element => {
    const color = element.getAttribute('data-color')
    switch(color) {
      case 'red':
        element.style.setProperty('color', 'rgb(255, 0, 0)', 'important')
        break
      case 'blue':
        element.style.setProperty('color', 'hsl(240, 75%, 60%)', 'important')
        break
      case 'green':
        element.style.setProperty('color', 'hsl(120, 75%, 60%)', 'important')
        break
      case 'gray':
        element.style.setProperty('color', 'rgb(77, 81, 86)', 'important')
        break
    }
  })
}

// 強制顏色樣式函數
const forceColorStyles = () => {
  const highlightsContainer = document.querySelector('.highlights-content')
  if (!highlightsContainer) return

  // 創建並注入強制樣式
  const styleId = 'force-highlights-colors'
  let existingStyle = document.getElementById(styleId)

  if (existingStyle) {
    existingStyle.remove()
  }

  const style = document.createElement('style')
  style.id = styleId
  style.textContent = `
    .highlights-content span[style*="color:hsl(120, 75%, 60%)"],
    .highlights-content span[style*="color: hsl(120, 75%, 60%)"] {
      color: hsl(120, 75%, 60%) !important;
    }

    .highlights-content span[style*="color:hsl(240, 75%, 60%)"],
    .highlights-content span[style*="color: hsl(240, 75%, 60%)"] {
      color: hsl(240, 75%, 60%) !important;
    }

    .highlights-content span[style*="color:rgb(255,0,0)"],
    .highlights-content span[style*="color: rgb(255, 0, 0)"] {
      color: rgb(255, 0, 0) !important;
    }

    .highlights-content .highlight-green {
      color: hsl(120, 75%, 60%) !important;
    }

    .highlights-content .highlight-blue {
      color: hsl(240, 75%, 60%) !important;
    }

    .highlights-content .highlight-red {
      color: rgb(255, 0, 0) !important;
    }
  `

  document.head.appendChild(style)
}

// 監聽路由參數變化，當商品ID改變時重新載入數據並滾動到頂部
watch(
  () => route.query.id,
  async (newId, oldId) => {
    if (newId && newId !== oldId) {
      // 立即多重滾動到頂部，確保不會保持在底部
      await nextTick()
      
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0
      window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
      
      // 延遲滾動以防止某些瀏覽器的異步問題
      setTimeout(() => {
        window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
      }, 0)
      
      // 重新載入商品數據
      try {
        loading.value = true
        error.value = null
        
        const res = await getProductById(newId)
        
        if (!res || !res.success || !res.product) {
          throw new Error('找不到此商品')
        }
        
        const product = res.product
        productData.value = product
        
        // 重新解析規格
        if (product.specTypes) {
          try {
            specTypes.value = JSON.parse(product.specTypes)
            
            // 重置選擇的規格
            Object.keys(selectedSpecs).forEach(key => {
              delete selectedSpecs[key]
            })
            
            specTypes.value.forEach(type => {
              selectedSpecs[type.name] = null
            })
          } catch (e) {
            console.error('規格類型解析失敗:', e)
            specTypes.value = []
          }
        }
        
        if (product.specCombinations) {
          try {
            specCombinations.value = JSON.parse(product.specCombinations)
          } catch (e) {
            console.error('規格組合解析失敗:', e)
            specCombinations.value = []
          }
        }
        
        // 數據載入完成後最終確認滾動位置
        await nextTick()
        window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
        
        // 修正重點商品資訊的內聯樣式
        await nextTick()
        fixHighlightsStyles()

        // 額外的強制顏色修正
        setTimeout(() => {
          forceInlineStyles() // 確保所有富文本內容的內聯樣式都能正確顯示
          fixHighlightsStyles()
          forceColorStyles()
        }, 100)
        
      } catch (err) {
        console.error('💥 重新載入商品失敗:', err)
        error.value = err.message || '載入商品失敗，請稍後再試'
      } finally {
        loading.value = false
      }
    }
  }
)

// 監聽標籤切換，確保富文本顏色正確顯示
watch(() => nowOptions.value, async (newOption) => {
  console.log('🔄 標籤切換:', newOption)

  // 等待 DOM 更新
  await nextTick()

  // 強制應用內聯樣式
  setTimeout(() => {
    forceInlineStyles()
    console.log('✅ 已為', newOption, '應用內聯樣式修正')
  }, 50)
}, { immediate: false })

// 新增：處理規格選擇
const selectSpec = (typeName, optionValue) => {
  if (selectedSpecs[typeName] === optionValue) {
    // 如果再次點擊已選中的規格，則取消選擇
    selectedSpecs[typeName] = null
  } else {
    selectedSpecs[typeName] = optionValue
  }
}

// 新增：計算當前選擇的規格組合
const currentCombination = computed(() => {
  // 檢查是否所有規格都已選擇
  const allSpecsSelected = Object.values(selectedSpecs).every(val => val !== null)
  
  if (!allSpecsSelected) return null
  
  // 將選擇的規格轉換為陣列，以匹配組合中的順序
  const selectedValues = specTypes.value.map(type => selectedSpecs[type.name])
  
  // 尋找匹配的規格組合
  return specCombinations.value.find(combo => 
    JSON.stringify(combo.specs) === JSON.stringify(selectedValues)
  )
})

// 新增：獲取當前價格
const currentPrice = computed(() => {
  if (currentCombination.value) {
    return isUser.value ? currentCombination.value.specialPrice || currentCombination.value.price : currentCombination.value.price
  }
  return isUser.value ? productData.value?.price2 || productData.value?.price1 : productData.value?.price1
})

// 處理重點商品資訊的 computed 屬性
const processedHighlights = computed(() => {
  if (!productData.value?.highlights) return ''

  let html = productData.value.highlights

  // 不轉換 HSL 顏色，保持原始格式但添加強制樣式
  // 為所有包含顏色的元素添加強制樣式和 class

  // 處理綠色 HSL
  html = html.replace(/(<[^>]*style="[^"]*color:\s*hsl\(120,\s*75%,\s*60%\)[^"]*"[^>]*>)/gi,
    '$1'.replace('>', ' data-force-color="green">'))

  // 處理藍色 HSL
  html = html.replace(/(<[^>]*style="[^"]*color:\s*hsl\(240,\s*75%,\s*60%\)[^"]*"[^>]*>)/gi,
    '$1'.replace('>', ' data-force-color="blue">'))

  // 處理紅色 RGB
  html = html.replace(/(<[^>]*style="[^"]*color:\s*rgb\(255,\s*0,\s*0\)[^"]*"[^>]*>)/gi,
    '$1'.replace('>', ' data-force-color="red">'))

  // 為所有包含顏色的元素添加 class 和強制樣式
  html = html.replace(/style="([^"]*color:\s*hsl\(120,\s*75%,\s*60%\)[^"]*)"/gi,
    'style="$1; color: hsl(120, 75%, 60%) !important;" class="highlight-green"')
  html = html.replace(/style="([^"]*color:\s*hsl\(240,\s*75%,\s*60%\)[^"]*)"/gi,
    'style="$1; color: hsl(240, 75%, 60%) !important;" class="highlight-blue"')
  html = html.replace(/style="([^"]*color:\s*rgb\(255,\s*0,\s*0\)[^"]*)"/gi,
    'style="$1; color: rgb(255, 0, 0) !important;" class="highlight-red"')

  return html
})


const handleMouseMove = (event) => {
  const img = event.target
  const rect = img.getBoundingClientRect()

  // 計算滑鼠在圖片上的相對位置
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 將相對位置轉換為百分比
  const offsetX = Math.max(0, Math.min(100, (x / rect.width) * 100))
  const offsetY = Math.max(0, Math.min(100, (y / rect.height) * 100))

  // 設置變換原點，確保在圖片範圍內
  img.style.transformOrigin = `${offsetX}% ${offsetY}%`
}

// 獲取圖片路徑
const getImagePath = (image) => {
  // 使用全局的 getImageUrl 函數處理圖片路徑
  return getImageUrl(image, 'product')
}

// 新增：處理縮略圖點擊
const handleThumbnailClick = (index) => {
  currentImageIndex.value = index
  if (swiperRef.value && swiperRef.value.$el) {
    const swiperInstance = swiperRef.value.$el.swiper
    if (swiperInstance) {
      swiperInstance.slideTo(index)
    }
  }
}

// 新增：處理 Swiper 滑動
const handleSlideChange = (swiper) => {
  currentImageIndex.value = swiper.realIndex || swiper.activeIndex
}

// 購物車數量調整功能
const increaseCartQuantity = (item) => {
  if (item.count >= 999) {
    message.warning('最多只能購買 999 件')
    return
  }
  item.count++
  item.total = item.count * item.price
  handelPinia.calcTotal()
}

const decreaseCartQuantity = (item) => {
  if (item.count > 1) {
    item.count--
    item.total = item.count * item.price
    handelPinia.calcTotal()
  }
}

const handleCartQuantityInput = (item, event) => {
  const value = parseInt(event.target.value)
  
  if (isNaN(value) || value < 1) {
    item.count = 1
  } else if (value > 999) {
    item.count = 999
    message.warning('最多只能購買 999 件')
  } else {
    item.count = value
  }
  
  item.total = item.count * item.price
  handelPinia.calcTotal()
}

const addCart = () => {
  // 檢查是否啟用規格但未選擇完整
  if (specTypes.value.length > 0 && !currentCombination.value) {
    message.warning('請選擇完整的商品規格')
    return
  }
  
  // 先檢查本地存儲中的登入狀態
  const isLogin = localStorage.getItem('isLogin')
  const token = localStorage.getItem('token')

  // 如果本地存儲顯示已登入但 isUser 狀態不一致，則同步狀態
  if (isLogin && token && !isUser.value) {
    isUser.value = true
    console.log('已同步用戶登入狀態')
  }

  // 加入購物車，預設數量為1
  handelPinia.addCarts({
    id: productData.value.id,
    name: productData.value.name,
    count: 1,
    price: currentPrice.value,
    img: productData.value.images[0],
    // 新增規格資訊
    specs: currentCombination.value ? currentCombination.value.specs.join(', ') : '',
    sku: currentCombination.value ? currentCombination.value.sku : null,
    // 新增最低購買數量
    minPurchaseQty: productData.value.minPurchaseQty || 1
  })
}

const buyNow = () => {
  // 檢查是否啟用規格但未選擇完整
  if (specTypes.value.length > 0 && !currentCombination.value) {
    message.warning('請選擇完整的商品規格')
    return
  }
  
  // 先檢查本地存儲中的登入狀態
  const isLogin = localStorage.getItem('isLogin')
  const token = localStorage.getItem('token')

  // 如果本地存儲顯示已登入但 isUser 狀態不一致，則同步狀態
  if (isLogin && token && !isUser.value) {
    isUser.value = true
    console.log('已同步用戶登入狀態')
  }

  // 先加入購物車，預設數量為1
  handelPinia.addCarts({
    id: productData.value.id,
    name: productData.value.name,
    count: 1,
    price: currentPrice.value,
    img: productData.value.images[0],
    // 新增規格資訊
    specs: currentCombination.value ? currentCombination.value.specs.join(', ') : '',
    sku: currentCombination.value ? currentCombination.value.sku : null,
    // 新增最低購買數量
    minPurchaseQty: productData.value.minPurchaseQty || 1
  })
  // 直接導向結帳頁面
  router.push('/check')
}

// 留言相關
const commentModalVisible = ref(false)
const commentContent = ref('')
const commentSubmitting = ref(false)

// 分享功能相關
const shareModalVisible = ref(false)
const shareUrl = ref('')
const shareContent = ref('')

// 裝置檢測
const isMobile = ref(false)

// 檢測是否為行動裝置（8寸以下螢幕視為手機版）
const checkMobileDevice = () => {
  // 主要以螢幕寬度判斷，768px以下視為手機版（對應8寸螢幕）
  const screenWidth = window.innerWidth
  const isSmallScreen = screenWidth <= 768

  // 輔助檢查User Agent
  const userAgent = navigator.userAgent || navigator.vendor || window.opera
  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile/i
  const isMobileUA = mobileRegex.test(userAgent.toLowerCase())

  // 螢幕寬度為主要判斷條件，User Agent為輔助
  isMobile.value = isSmallScreen || isMobileUA
}

// 生成分享內容
const generateShareContent = () => {
  const currentUrl = window.location.href
  shareUrl.value = currentUrl
  
  // 獲取零售價格（原價），不論用戶是否為會員
  const retailPrice = currentCombination.value ? currentCombination.value.price : productData.value?.price1
  
  // 簡短描述（取前100字符）
  const shortDescription = productData.value?.description 
    ? productData.value.description.replace(/<[^>]*>/g, '').substring(0, 100) + '...'
    : '精選商品'
  
  shareContent.value = `🛍️ ${productData.value?.name}

📝 ${shortDescription}

💰 零售價：NT$ ${retailPrice}

🔗 立即查看：${currentUrl}

#優質商品 #限時推薦`
}

// 開啟分享模態框或直接複製連結（根據裝置類型）
const openShareModal = () => {
  if (isMobile.value) {
    // 手機版：開啟分享彈窗
    generateShareContent()
    shareModalVisible.value = true
  } else {
    // 電腦版：直接複製商品連結
    const currentUrl = window.location.href
    copyToClipboard(currentUrl)
  }
}

// 複製連結到剪貼板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('已複製到剪貼板！')
  } catch (err) {
    // 如果瀏覽器不支援 navigator.clipboard，使用舊方法
    const textarea = document.createElement('textarea')
    textarea.value = text
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
    message.success('已複製到剪貼板！')
  }
}

// 複製分享內容
const copyShareContent = () => {
  copyToClipboard(shareContent.value)
}

// 複製商品連結
const copyProductUrl = () => {
  copyToClipboard(shareUrl.value)
}

// 分享到社群媒體
const shareToSocial = (platform) => {
  const url = encodeURIComponent(shareUrl.value)
  const text = encodeURIComponent(`${productData.value?.name} - 精選商品推薦`)
  
  let shareLink = ''
  
  switch (platform) {
    case 'facebook':
      shareLink = `https://www.facebook.com/sharer/sharer.php?u=${url}`
      break
    case 'line':
      shareLink = `https://line.me/R/msg/text/?${encodeURIComponent(shareContent.value)}`
      break
    case 'twitter':
      shareLink = `https://twitter.com/intent/tweet?url=${url}&text=${text}`
      break
    default:
      return
  }
  
  window.open(shareLink, '_blank', 'width=600,height=400')
  message.success(`已開啟${platform}分享頁面`)
}

const openCommentModal = () => {
  // 檢查用戶是否登入
  if (!isUser.value) {
    message.warning('請先登入再進行留言')
    setTimeout(() => {
      router.push('/login')
    }, 1000)
    return
  }

  commentModalVisible.value = true
  commentContent.value = ''
}

const submitComment = async () => {
  if (!commentContent.value.trim()) {
    message.warning('留言內容不能為空')
    return
  }

  commentSubmitting.value = true

  try {
    const res = await postApiData('comments', {
      content: commentContent.value,
      product: productData.value.id,
    })

    if (res && res.status === 201) {
      Modal.success({
        title: '留言成功',
        content: '您的留言已成功送出，感謝您的分享！',
        centered: true,
      })
      commentModalVisible.value = false
      commentContent.value = ''
    } else {
      message.error(res?.error || '留言失敗，請稍後再試')
    }
  } catch (error) {
    console.error('提交留言時發生錯誤:', error)
    message.error('留言失敗，請稍後再試')
  } finally {
    commentSubmitting.value = false
  }
}

// 新增：處理滾動事件
const handleScroll = () => {
  // 嘗試多種方式獲取滾動位置
  const scrollTop1 = window.pageYOffset
  const scrollTop2 = document.documentElement.scrollTop
  const scrollTop3 = document.body.scrollTop
  const scrollTop4 = window.scrollY
  
  // 優先使用 document.body.scrollTop，因為這是實際的滾動位置
  const scrollTop = scrollTop3 || scrollTop1 || scrollTop2 || scrollTop4 || 0
  
  const shouldShow = scrollTop > 100
  showBackToTop.value = shouldShow
}

// 新增：回到頂部方法
const scrollToTop = () => {
  // 直接設置所有可能的滾動位置為 0
  window.scrollTo(0, 0)
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0
}


</script>

<template>
  <div class="product-info-container container m-auto px-4 pt-8">
    <!-- 載入狀態 -->
    <div v-if="loading" class="loading-container">
      <!-- 載入指示器 -->
      <div class="flex justify-center items-center mb-8">
        <div class="spinner border-4 border-blue-200 border-t-blue-600 rounded-full w-8 h-8 animate-spin mr-3"></div>
        <span class="text-lg">{{ loadingText }}</span>
      </div>
      
      <!-- 商品詳情骨架屏 -->
      <div class="flex flex-col lg:flex-row items-start animate-pulse">
        <!-- 圖片區域骨架屏 -->
        <div class="lg:w-5/12 lg:sticky lg:top-24">
          <div class="h-[500px] aspect-square bg-gray-200 rounded-lg mb-4"></div>
          <div class="flex space-x-2">
            <div class="w-16 h-16 bg-gray-200 rounded"></div>
            <div class="w-16 h-16 bg-gray-200 rounded"></div>
            <div class="w-16 h-16 bg-gray-200 rounded"></div>
          </div>
        </div>
        
        <!-- 商品資訊區域骨架屏 -->
        <div class="lg:w-7/12 text-center lg:text-left lg:pl-16 flex flex-col mt-8 lg:mt-0">
          <!-- 商品名稱 -->
          <div class="h-10 bg-gray-200 rounded mb-4"></div>
          
          <!-- 價格 -->
          <div class="h-12 bg-gray-200 rounded w-1/2 mb-6"></div>
          
          <!-- 規格選擇 -->
          <div class="mb-6">
            <div class="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div class="flex space-x-2">
              <div class="h-10 bg-gray-200 rounded w-20"></div>
              <div class="h-10 bg-gray-200 rounded w-20"></div>
              <div class="h-10 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
          
          <!-- 按鈕 -->
          <div class="flex space-x-4">
            <div class="h-10 bg-gray-200 rounded w-32"></div>
            <div class="h-10 bg-gray-200 rounded w-32"></div>
          </div>
          
          <!-- 操作按鈕 -->
          <div class="flex space-x-4 mt-6">
            <div class="h-8 bg-gray-200 rounded w-24"></div>
            <div class="h-8 bg-gray-200 rounded w-24"></div>
          </div>
        </div>
      </div>
      
      <!-- 商品說明骨架屏 -->
      <div class="my-8">
        <div class="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded w-5/6"></div>
          <div class="h-4 bg-gray-200 rounded w-4/6"></div>
        </div>
      </div>
    </div>

    <!-- 錯誤狀態 -->
    <div v-else-if="error" class="error-container text-center py-12">
      <div class="text-red-500 text-6xl mb-6">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h2 class="text-2xl font-bold text-gray-800 mb-4">載入失敗</h2>
      <p class="text-gray-600 mb-6">{{ error }}</p>
      <div class="space-x-4">
        <a-button type="primary" size="large" @click="$router.go(-1)">
          <i class="fas fa-arrow-left mr-2"></i>返回上頁
        </a-button>
        <a-button size="large" @click="window.location.reload()">
          <i class="fas fa-redo mr-2"></i>重新載入
        </a-button>
      </div>
    </div>

    <!-- 正常內容 -->
    <div v-else-if="productData">
      <div class="flex flex-col lg:flex-row items-start">
        <div class="w-full lg:w-5/12 lg:sticky lg:top-24 flex flex-col items-center lg:items-start">
          <!-- 主圖片輪播 -->
          <swiper
            ref="swiperRef"
            :slidesPerView="1"
            :spaceBetween="30"
            :loop="productData?.images.length > 1"
            :pagination="{
              clickable: true,
            }"
            :navigation="true"
            :modules="modules"
            @slideChange="handleSlideChange"
            class="w-full max-w-[500px] h-[500px] aspect-square product-swiper mb-4"
          >
            <swiper-slide
              class=""
              v-if="productData?.images && productData?.images.length > 0"
              v-for="i in productData?.images"
              :key="i"
            >
              <div class="group relative cursor-[zoom-in] overflow-hidden h-full">
                <img
                  :src="getImagePath(i)"
                  :alt="productData?.name"
                  class="group-hover:scale-[1.5] duration-300 w-full h-full object-contain"
                  @mousemove="handleMouseMove"
                />
              </div>
            </swiper-slide>
            <swiper-slide v-else>
              <div
                class="aspect-square flex items-center justify-center bg-transparent text-gray-500"
              >
                無圖片
              </div>
            </swiper-slide>
          </swiper>
          
          <!-- 縮略圖列表 -->
          <div v-if="productData?.images && productData?.images.length > 1" class="thumbnail-container">
            <div class="flex gap-2 overflow-x-auto pb-2">
              <div
                v-for="(image, index) in productData.images"
                :key="`thumb-${index}`"
                @click="handleThumbnailClick(index)"
                :class="[
                  'thumbnail-item',
                  'flex-shrink-0',
                  'w-20 h-20',
                  'border-2 rounded-lg overflow-hidden cursor-pointer transition-all',
                  currentImageIndex === index 
                    ? 'border-blue-500 shadow-lg' 
                    : 'border-gray-300 hover:border-gray-400'
                ]"
              >
                <img
                  :src="getImagePath(image)"
                  :alt="`${productData.name} - 圖片 ${index + 1}`"
                  class="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="lg:w-7/12 text-center lg:text-left lg:pl-16 flex flex-col">
          <div>
            <h3 class="font-bold text-4xl mb-4">{{ productData?.name }}</h3>

            <!-- 重點商品資訊 -->
            <div v-if="productData?.highlights && productData.highlights.trim() !== ''" class="des border-bottom mb-4">
              <div v-html="processedHighlights" class="rich-text-content highlights-content"></div>
            </div>

            <div class="my-4">
              <div v-if="isUser" class="flex items-baseline space-x-2">
                <a-tag color="red" class="text-base">會員價</a-tag>
                <span class="text-4xl font-extrabold text-red-600">
                  NT$ <TwDoptions :number="currentPrice" />
                </span>
              </div>
              <div v-else class="text-4xl font-extrabold text-gray-900">
                NT$ <TwDoptions :number="currentPrice" />
              </div>
            </div>

            <!-- 新增：規格選擇 -->
            <div v-if="specTypes.length > 0" class="my-6 spec-selection">
              <div v-for="specType in specTypes" :key="specType.name" class="mb-4">
                <h4 class="font-semibold mb-2 text-center lg:text-left">{{ specType.name }}</h4>
                <div class="spec-options flex flex-wrap gap-2 justify-center lg:justify-start">
                  <button
                    v-for="option in specType.options"
                    :key="option.value"
                    @click="selectSpec(specType.name, option.value)"
                    :class="[
                      'px-4 py-2 border rounded-md transition-colors spec-option-btn',
                      selectedSpecs[specType.name] === option.value
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-500',
                    ]"
                  >
                    {{ option.value }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-auto">
            <div v-if="productData?.format2">
              <div>{{ productData?.format1 }}</div>
              <div class="bg-gray-200 inline-block px-2 border-2 border-black">
                {{ productData?.format2 }}
              </div>
            </div>
            
                        
            <div class="flex items-center justify-center lg:justify-start">
              <a-button 
                class="mr-4" 
                size="large" 
                @click="addCart"
              > 
                加入購物車 
              </a-button>
              <a-button 
                type="primary" 
                size="large" 
                @click="buyNow"
              > 
                立即購買 
              </a-button>
            </div>
            <div class="mt-6 text-center lg:text-left">
              <a-button type="link" @click="openCommentModal">
                <i class="fas fa-comment-dots mr-2"></i> 我要留言
              </a-button>
              <a-button type="link" @click="openShareModal" class="ml-4">
                <i :class="isMobile ? 'fas fa-share-alt mr-2' : 'fas fa-copy mr-2'"></i>
                {{ isMobile ? '分享商品' : '複製連結' }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
      <div class="my-8">
        <div class="mb-3">
          <a-segmented v-model:value.trim.lazy="nowOptions" :options="optionsData" />
        </div>
        <template v-if="nowOptions == '商品說明'">
          <div v-html="productData?.description" class="rich-text-content"></div>
        </template>
        <template v-else-if="nowOptions == '商品規格'">
          <div v-html="productData?.desc2" class="rich-text-content"></div>
        </template>
      </div>
    </div>
  </div>

  <!-- 留言彈窗 -->
  <a-modal
    v-model:open="commentModalVisible"
    title="留言此商品"
    :maskClosable="false"
    @ok="submitComment"
    okText="提交留言"
    cancelText="取消"
    :confirmLoading="commentSubmitting"
  >
    <p class="mb-2">請輸入您對「{{ productData?.name }}」的留言：</p>
    <a-textarea
      v-model:value="commentContent"
      placeholder="請輸入留言內容..."
      :rows="4"
      :maxLength="500"
      showCount
    />
  </a-modal>

  <!-- 分享彈窗 -->
  <a-modal
    v-model:open="shareModalVisible"
    title="分享商品"
    :footer="null"
    width="600px"
    :maskClosable="true"
    centered
  >
    <div class="space-y-6">
      <!-- 商品預覽 -->
      <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
        <img 
          v-if="productData?.images?.[0]" 
          :src="getImagePath(productData.images[0])" 
          :alt="productData?.name"
          class="w-16 h-16 object-cover rounded"
        >
        <div class="flex-1">
          <h4 class="font-semibold">{{ productData?.name }}</h4>
          <p class="text-gray-600">NT$ {{ currentPrice }}</p>
        </div>
      </div>

      <!-- 分享內容 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">分享內容</label>
        <a-textarea
          v-model:value="shareContent"
          :rows="6"
          readonly
          class="bg-gray-50"
        />
        <a-button 
          type="link" 
          @click="copyShareContent"
          class="mt-2 p-0"
        >
          <i class="fas fa-copy mr-1"></i>複製內容
        </a-button>
      </div>

      <!-- 商品連結 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">商品連結</label>
        <div class="flex">
          <a-input
            v-model:value="shareUrl"
            readonly
            class="flex-1"
          />
          <a-button 
            type="primary" 
            @click="copyProductUrl"
            class="ml-2"
          >
            <i class="fas fa-copy mr-1"></i>複製
          </a-button>
        </div>
      </div>

      <!-- 社群分享按鈕 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">分享到社群媒體</label>
        <div class="flex space-x-3">
          <a-button 
            @click="shareToSocial('facebook')"
            class="flex-1 text-blue-600 border-blue-600 hover:bg-blue-50"
          >
            <i class="fab fa-facebook-f mr-2"></i>Facebook
          </a-button>
          <a-button
            v-if="isMobile"
            @click="shareToSocial('line')"
            class="flex-1 text-green-600 border-green-600 hover:bg-green-50"
          >
            <i class="fab fa-line mr-2"></i>LINE
          </a-button>
          <a-button 
            @click="shareToSocial('twitter')"
            class="flex-1 text-blue-400 border-blue-400 hover:bg-blue-50"
          >
            <i class="fab fa-twitter mr-2"></i>Twitter
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 回到頂部按鈕 -->
  <div 
    v-show="showBackToTop" 
    @click="scrollToTop"
    class="back-to-top-btn"
    title="回到頂部"
  >
    ↑
  </div>
</template>

<style scoped>
/* 引入富文本樣式 */
@import '../assets/styles/rich-text.css';
/* 引入重點商品資訊專用樣式 */
@import '../assets/styles/highlights.css';
/* 引入內聯樣式修復 CSS */
@import '../assets/styles/inline-styles-fix.css';

.product-info-container {
  min-height: 100vh;
}

/* 重點商品資訊樣式已移至 highlights.css */

/* 重點商品資訊內容樣式 */
.highlights-content {
  text-align: left;
}

/* 支援富文本編輯���的內聯樣式 */
.highlights-content :deep(*[style]) {
  /* 不強制覆蓋內聯樣式，讓它們自然生效 */
}

/* 標題樣式 - 保持編輯器設定的樣式 */
.highlights-content :deep(h1) {
  margin-bottom: 0px !important;
  flex-shrink: 1;
  overflow-wrap: break-word;
  hyphens: auto;
}

.highlights-content :deep(h2),
.highlights-content :deep(h3),
.highlights-content :deep(h4),
.highlights-content :deep(h5),
.highlights-content :deep(h6) {
  margin-bottom: 0px !important;
  white-space-collapse: preserve;
  flex-shrink: 1;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* span 元素樣式 */
.highlights-content :deep(span) {
  flex-shrink: 1;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 段落樣式 */
.highlights-content :deep(p) {
  margin-bottom: 0.5rem;
}

/* 列表樣式 */
.highlights-content :deep(ul),
.highlights-content :deep(ol) {
  margin-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.highlights-content :deep(li) {
  margin-bottom: 0.25rem;
}

/* 支援字體樣式 */
.highlights-content :deep(b),
.highlights-content :deep(strong) {
  font-weight: bold !important;
}

.highlights-content :deep(i),
.highlights-content :deep(em) {
  font-style: italic !important;
}

.highlights-content :deep(u) {
  text-decoration: underline !important;
}

.highlights-content :deep(s),
.highlights-content :deep(strike) {
  text-decoration: line-through !important;
}

/* 支援文字對齊 */
.highlights-content :deep([style*="text-align: left"]) {
  text-align: left !important;
}

.highlights-content :deep([style*="text-align: center"]) {
  text-align: center !important;
}

.highlights-content :deep([style*="text-align: right"]) {
  text-align: right !important;
}

.highlights-content :deep([style*="text-align: justify"]) {
  text-align: justify !important;
}

/* 支援字體大小 - 讓內聯樣式自然生效 */
.highlights-content :deep([style*="font-size"]) {
  /* 內聯樣式會自動覆蓋這裡的設定 */
}

/* 支援字體顏色 - 讓內聯樣式自然生效 */
.highlights-content :deep([style*="color"]) {
  /* 內聯樣式會自動覆蓋這裡的設定 */
}

/* 支援背景顏色 - 讓內聯樣式自然生效 */
.highlights-content :deep([style*="background-color"]) {
  /* 內聯樣式會自動覆蓋這裡的設定 */
}

/* 支援字體家族 - 讓內聯樣式自然生效 */
.highlights-content :deep([style*="font-family"]) {
  /* 內聯樣式會自動覆蓋這裡的設定 */
}

/* 確保中文字體正確顯示 */
.highlights-content :deep([style*="微軟正黑體"]) {
  font-family: "微軟正黑體", "Microsoft JhengHei", sans-serif !important;
}

/* 強制特定顏色顯示 - 覆蓋所有可能的全域樣式 */
.highlights-content :deep(span[style*="color:hsl(120, 75%, 60%)"]),
.highlights-content :deep(span[style*="color: hsl(120, 75%, 60%)"]) {
  color: hsl(120, 75%, 60%) !important;
}

.highlights-content :deep(span[style*="color:hsl(240, 75%, 60%)"]),
.highlights-content :deep(span[style*="color: hsl(240, 75%, 60%)"]) {
  color: hsl(240, 75%, 60%) !important;
}

.highlights-content :deep(span[style*="color:rgb(255,0,0)"]),
.highlights-content :deep(span[style*="color: rgb(255, 0, 0)"]) {
  color: rgb(255, 0, 0) !important;
}

/* 強制 class 顏色 */
.highlights-content :deep(.highlight-green) {
  color: hsl(120, 75%, 60%) !important;
}

.highlights-content :deep(.highlight-blue) {
  color: hsl(240, 75%, 60%) !important;
}

.highlights-content :deep(.highlight-red) {
  color: rgb(255, 0, 0) !important;
}

.highlights-content :deep([style*="新細明體"]) {
  font-family: "新細明體", "PMingLiU", serif !important;
}

.highlights-content :deep([style*="標楷體"]) {
  font-family: "標楷體", "DFKai-SB", serif !important;
}

.highlights-content :deep([style*="Arial"]) {
  font-family: Arial, sans-serif !important;
}

.highlights-content :deep([style*="Tahoma"]) {
  font-family: Tahoma, sans-serif !important;
}

/* 表格樣式 */
.highlights-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.highlights-content :deep(table td),
.highlights-content :deep(table th) {
  border: 1px solid #ddd;
  padding: 8px;
}

/* 連結樣式 */
.highlights-content :deep(a) {
  color: #0066cc;
  text-decoration: underline;
}

.highlights-content :deep(a:hover) {
  color: #0052a3;
}

/* 引用樣式 */
.highlights-content :deep(blockquote) {
  border-left: 4px solid #ccc;
  margin-left: 0;
  padding-left: 16px;
  font-style: italic;
}

/* 確保 white-space 樣式正確 */
.highlights-content :deep([style*="white-space-collapse: preserve"]) {
  white-space: pre-wrap !important;
}

/* 確保 flex 相關樣式正確 */
.highlights-content :deep([style*="flex-shrink"]) {
  display: inline-block;
}

/* 修正 margin 和 padding */
.highlights-content :deep([style*="margin-bottom: 0px"]) {
  margin-bottom: 0 !important;
}

/* 確保內容不會溢出 */
.highlights-content :deep(*) {
  max-width: 100%;
  word-wrap: break-word;
}

/* 載入動畫 */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 骨架屏動畫 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse div {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 載入狀態過渡 */
.loading-container,
.error-container {
  transition: opacity 0.3s ease-in-out;
}

/* 商品內容淡入效果 */
.product-content {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Swiper 樣式優化 */
.product-swiper {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.product-swiper .swiper-button-prev,
.product-swiper .swiper-button-next {
  color: white;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.product-swiper .swiper-button-prev::after,
.product-swiper .swiper-button-next::after {
  font-size: 16px;
}

.product-swiper .swiper-pagination-bullet {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.product-swiper .swiper-pagination-bullet-active {
  background: #1890ff;
}

/* 圖片縮放效果 */
.product-swiper img {
  transition: transform 0.3s ease;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 縮略圖容器樣式 */
.thumbnail-container {
  max-width: 100%;
  position: relative;
}

/* 縮略圖滾動條樣式 */
.thumbnail-container .flex {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.thumbnail-container .flex::-webkit-scrollbar {
  height: 6px;
}

.thumbnail-container .flex::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.thumbnail-container .flex::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.thumbnail-container .flex::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 縮略圖項目樣式 */
.thumbnail-item {
  position: relative;
  background: #f7fafc;
}

.thumbnail-item::after {
  content: '';
  position: absolute;
  inset: 0;
  background: transparent;
  transition: background 0.2s;
}

.thumbnail-item:hover::after {
  background: rgba(0, 0, 0, 0.1);
}

.thumbnail-item.border-blue-500::after {
  background: transparent;
}

/* 縮略圖圖片樣式 */
.thumbnail-item img {
  transition: transform 0.2s;
}

.thumbnail-item:hover img {
  transform: scale(1.05);
}

/* 錯誤狀態樣式 */
.error-container {
  background: linear-gradient(135deg, #fef7f7 0%, #fdf2f2 100%);
  border-radius: 12px;
  border: 1px solid #fed7d7;
}

/* 響應式優化 */
@media (max-width: 1024px) {
  .product-info-container {
    padding: 1rem;
  }
  
  .product-swiper {
    height: 400px;
  }
  
  .thumbnail-item {
    width: 70px;
    height: 70px;
  }
}

@media (max-width: 640px) {
  .product-swiper {
    height: 300px;
  }

  .product-info-container {
    padding: 0.5rem;
  }

  .thumbnail-item {
    width: 60px;
    height: 60px;
  }

  .thumbnail-container .flex {
    gap: 0.5rem;
  }

  /* 手機版規格選擇優化 */
  .spec-selection {
    text-align: center;
  }

  .spec-selection h4 {
    text-align: center;
    margin-bottom: 0.75rem;
  }

  .spec-options {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .spec-options button {
    min-width: 80px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* 改善購物車表格在小螢幕的顯示 */
@media (max-width: 768px) {
  .cart-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}

/* 載入狀態的視覺回饋 */
.loading-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

/* 商品資訊卡片效果 */
.product-details {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin: 1rem 0;
}

/* 規格選擇按鈕效果 */
.spec-option {
  transition: all 0.2s ease;
  border: 2px solid #e5e7eb;
  background: white;
}

.spec-option:hover {
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.spec-option.selected {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

/* 規格選擇區域樣式 */
.spec-selection {
  width: 100%;
  margin: 1.5rem auto;
}

.spec-option-btn {
  margin: 0.25rem;
  min-width: 4rem;
  text-align: center;
}

/* 手機版規格選擇優化 */
@media (max-width: 768px) {
  .spec-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .spec-selection > div {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .spec-options {
    width: 100%;
    justify-content: center;
    padding: 0 0.5rem;
  }
}


/* 禁用按鈕樣式 */
.ant-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 購物車數量輸入框樣式 */
.cart-quantity-input input[type="number"]::-webkit-inner-spin-button,
.cart-quantity-input input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.cart-quantity-input input[type="number"] {
  -moz-appearance: textfield;
}

/* 數量調整按鈕樣式 */
.cart-quantity-input button:hover:not(:disabled) {
  background-color: #f3f4f6;
}

/* 輸入框聚焦樣式 */
.cart-quantity-input input[type="number"]:focus {
  outline: none;
  border-color: #3b82f6;
}

/* 回到頂部按鈕樣式 */
.back-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgb(255, 255, 255);
  border: none;
  font-weight: 60;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: rgb(20, 20, 20);
  font-size: 20px;
}

.back-to-top-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .back-to-top-btn {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}
</style>

<style>
/* Ant Design 按鈕樣式覆蓋 */
.ant-btn-primary span {
  color: #000 !important;
}
.ant-btn-primary {
  background: #fff !important;
  border-color: #d9d9d9 !important;
  color: #000 !important;
}
.ant-btn-primary:hover, .ant-btn-primary:focus {
  background: #fff !important;
  border-color: #000 !important;
  color: #000 !important;
}

/* 分享按鈕樣式 */
.ant-btn.bg-blue-600 {
  background: #2563eb !important;
  border-color: #2563eb !important;
  color: #fff !important;
}
.ant-btn.bg-blue-600:hover {
  background: #1d4ed8 !important;
  border-color: #1d4ed8 !important;
  color: #fff !important;
}

.ant-btn.bg-green-500 {
  background: #10b981 !important;
  border-color: #10b981 !important;
  color: #fff !important;
}
.ant-btn.bg-green-500:hover {
  background: #059669 !important;
  border-color: #059669 !important;
  color: #fff !important;
}

.ant-btn.bg-blue-400 {
  background: #60a5fa !important;
  border-color: #60a5fa !important;
  color: #fff !important;
}
.ant-btn.bg-blue-400:hover {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #fff !important;
}
</style>
