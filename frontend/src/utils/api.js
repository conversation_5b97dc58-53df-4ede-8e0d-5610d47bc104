import { message } from 'ant-design-vue'
import axios from 'axios'
import router from '@/router'
import { API_BASE_URL, getApiUrl, getImageUrl, API_CONFIG } from '@/config/apiConfig'

// 創建 axios 實例
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_CONFIG.timeout,
  withCredentials: API_CONFIG.withCredentials,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 請求重試機制
axiosInstance.interceptors.request.use(
  async (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 響應攔截器 - 增強版本
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const config = error.config

    // 處理認證錯誤 (401, 403)
    if (error.response) {
      const { status, data } = error.response
      
      if (status === 401) {
        console.warn('🔐 收到401未授權錯誤 - Token可能已過期')
        handleTokenExpired('前台API')
        return Promise.reject(error)
      }
      
      if (status === 403) {
        console.warn('🚫 收到403禁止訪問錯誤 - 權限不足')
        if (data?.message?.includes('token') || data?.message?.includes('過期')) {
          handleTokenExpired('前台API')
        }
        return Promise.reject(error)
      }
      
      // 處理其他錯誤狀態
      if (status >= 500) {
        console.error('🔥 伺服器錯誤:', status, data?.message)
        message.error('伺服器錯誤，請稍後再試')
        return Promise.reject(error)
      }
    }

    // 處理網路錯誤
    if (!error.response && error.code === 'ECONNABORTED') {
      console.error('⏱️ 請求超時')
      message.error('請求超時，請檢查網路連線')
      return Promise.reject(error)
    }

    // 重試邏輯 (僅對非認證錯誤重試)
    if (error.response?.status !== 401 && error.response?.status !== 403) {
      // 如果沒有設定重試次數，則設定為 0
      config.__retryCount = config.__retryCount || 0

      // 檢查是否達到最大重試次數
      if (config.__retryCount >= API_CONFIG.retry) {
        return Promise.reject(error)
      }

      // 增加重試次數
      config.__retryCount += 1

      console.log(`🔄 重試請求 (${config.__retryCount}/${API_CONFIG.retry}):`, config.url)

      // 創建新的 Promise 來處理重試
      const backoff = new Promise((resolve) => {
        setTimeout(() => {
          resolve()
        }, API_CONFIG.retryDelay * config.__retryCount)
      })

      // 等待延遲後重試請求
      await backoff
      return axiosInstance(config)
    }

    return Promise.reject(error)
  }
)

// 統一的 Token 過期處理函數
const handleTokenExpired = (source = '前台') => {
  console.warn(`🔐 ${source} Token已過期或無效，執行強制登出`)
  
  // 顯示提示訊息
  message.warning('登入已過期，請重新登入', 3)
  
  // 清除所有登入相關資料
  clearAuthData()
  
  // 延遲跳轉，讓用戶看到提示訊息
  setTimeout(() => {
    forceLogout()
  }, 1000)
}

// 清除認證資料
const clearAuthData = () => {
  try {
    // 清除 localStorage 中的認證資料
    const authKeys = ['token', 'isLogin', 'user', 'userData', 'belongCp']
    authKeys.forEach(key => {
      localStorage.removeItem(key)
    })
    
    // 清除 sessionStorage 中的認證資料
    const sessionKeys = ['userSession', 'authSession']
    sessionKeys.forEach(key => {
      sessionStorage.removeItem(key)
    })
    
    console.log('✅ 已清除所有認證資料')
  } catch (error) {
    console.error('❌ 清除認證資料時發生錯誤:', error)
  }
}

// 強制登出並跳轉
const forceLogout = () => {
  try {
    // 清除 Pinia store 中的用戶狀態 (如果有的話)
    if (window.piniaStore) {
      window.piniaStore.$reset?.()
    }
    
    // 判斷當前路由，避免重複跳轉
    const currentPath = window.location.pathname
    if (currentPath !== '/login' && currentPath !== '/') {
      console.log('🔄 重導向到登入頁面')
      router.push('/login').catch(err => {
        console.warn('路由跳轉警告:', err)
        // 如果路由跳轉失敗，使用 window.location 強制跳轉
        window.location.href = '/login'
      })
    }
  } catch (error) {
    console.error('❌ 強制登出時發生錯誤:', error)
    // 最後手段：強制重新載入頁面到登入頁
    window.location.href = '/login'
  }
}

// 處理401錯誤的函數 - 已更新為使用統一的token過期處理
const handle401Error = (apiType = '') => {
  console.warn(`⚠️ API ${apiType} 收到401錯誤，使用統一的token過期處理`)
  handleTokenExpired(`前台API-${apiType}`)
}

const getHeaders = () => ({
  'Content-Type': 'application/json',
  Authorization: `Bearer ${localStorage.getItem('token')}`,
})

const getAdminHeaders = () => {
  // 簡化版本，不進行令牌驗證
  // console.log('使用簡化版管理員權限檢查')

  return {
    'Content-Type': 'application/json',
    // 不使用真正的令牌驗證
    'X-Admin-Access': 'true',
  }
}

const getParams = () => ({
  userId: localStorage.getItem('user'),
  belongCp: localStorage.getItem('belongCp'),
})

export default function () {
  // const apiDatasStores = apiStores()

  let resData = null
  let resTotal = null
  let resTotalMoney = null

  // 這些函數已在文件末尾定義，這裡移除重複定義
  // const apiStore = useApiStores()
  const cancelTokenSource = axios.CancelToken.source()
  const getApiData = async function (e, i) {
    try {
      // 根據不同的API類型調整路徑
      let apiPath = e
      if (e === 'product') {
        apiPath = 'products'
      } else if (e === 'basic') {
        apiPath = 'basic/info'
      } else if (e === 'user') {
        apiPath = 'users/profile'
      } else if (e === 'orderList') {
        apiPath = 'orderList'
      } else if (e === 'member/orders') {
        apiPath = 'member/orders'
      } else if (e === 'comments') {
        apiPath = `basic/comments`
      } else if (e === 'user/comments') {
        apiPath = `basic/user/comments`
      }

      const res = await axios.get(getApiUrl(apiPath), {
        params: {
          ...(i || {}),
          sort: i?.sort === '特色商品' ? '特色商品' : i?.sort,
        },
        headers: getHeaders(),
        cancelToken: cancelTokenSource.token,
      })

      // 確保API響應有效
      if (!res || !res.data) {
        console.error('API響應無效:', res)
        return { resData: [], resTotal: 0 }
      }

      // 源順食品分類請求特別處理
      if (e === 'product' && i?.sort && i.sort.includes('源順')) {
        // console.log('📢 源順食品API返回:', res.data)
      }

      // 🔧 修復：統一處理API返回的資料結構
      if (e === 'product') {
        let products = []
        let totalCount = 0
        
        // 嘗試不同的數據結構
        if (res.data.body) {
          products = res.data.body
          totalCount = res.data.total || products.length
        } else if (res.data.resData) {
          products = res.data.resData
          totalCount = res.data.resTotal || res.data.total || products.length
        } else {
          products = []
          totalCount = 0
        }
        
        // console.log(`🔢 商品數據解析: 找到${products.length}件商品，總數${totalCount}`)
        
        return {
          resData: products,
          resTotal: totalCount,
          resTotalMoney: res.data.totalMoney || 0
        }
      } else if (e === 'user') {
        // 其他API處理邏輯保持不變
        resData = res.data.body
        return { resData, resTotal, resTotalMoney, success: res.data.success }
      } else if (e === 'orderList' || e === 'member/orders') {
        // 會員訂單和訂單列表數據處理
        resData = res.data.resData || []
        resTotal = res.data.total || 0
        resTotalMoney = res.data.totalMoney || 0
        return { resData, resTotal, resTotalMoney, success: res.data.success }
      } else if (e === 'brands') {
        // 品牌API處理
        return {
          resData: res.data.resData || [],
          success: res.data.success,
          resTotal: res.data.total,
          resTotalMoney: res.data.totalMoney,
        }
      } else if (e === 'comments' || e === 'user/comments') {
        // 留言API處理
        return {
          resData: res.data.resData || [],
          error: res.data.error,
          status: res.data.status,
        }
      } else if (e === 'basic/carousel') {
        // 輪播圖API處理
        return {
          success: res.data.success,
          data: res.data.data || [],
          error: res.data.error,
        }
      } else {
        // 默認處理方式
        resTotalMoney = res.data.totalMoney
        resTotal = res.data.total
        resData = res.data.body
        return { resData, resTotal, resTotalMoney }
      }
    } catch (error) {
      console.error('API 請求錯誤:', error)

      // 如果是401錯誤，使用更智能的處理方式
      if (error.response && error.response.status === 401) {
        handle401Error(e)
      }

      return { resData: [], resTotal: 0, error: error.message }
    }
  }
  const patchApiData = async function (e, changData) {
    try {
      // 建立 params 和 headers 的複製，以免修改到原本的資料
      const params = { ...getParams() }
      const updatedHeaders = { ...getHeaders() }
      if (e == 'product') {
        params.shop = changData.shop ?? localStorage.shop
        params.belongCp = ********
        params.userId = 'chengshe'
      } else if (e == 'user') {
        params.userId = changData.userId
        params.id = changData.id
        updatedHeaders.Authorization = `Bearer ${localStorage.getItem('token')}`
      } else if (e == 'orderList' && changData.status == 3) {
        params.isMail = 1
      } else if (e == 'basic') {
        params.ID = getParams().belongCp
      }

      // 修改API路徑處理邏輯
      let apiPath = `${e}/update`

      // 特殊處理用戶資料更新API
      if (e === 'user') {
        apiPath = `users/update/${changData.id}`
      } else if (e.startsWith('comments/')) {
        // 處理評論相關的API，例如 comments/123/read
        apiPath = `basic/${e}`
      }

      const res = await axiosInstance.patch(apiPath, changData, {
        params: {
          ...params,
        },
        headers: updatedHeaders,
      })
      // console.log(changData)
      return res
    } catch (error) {
      console.error('Error fetching client data:', error)
      // message.error('發生錯誤!')
      return { status: 500, error: error.message }
    }
  }

  const updateApiData = async function (e, updateData) {
    try {
      // 建立 params 和 headers 的複製，以免修改到原本的資料
      const params = { ...getParams() }
      const updatedHeaders = { ...getHeaders() }

      const res = await axiosInstance.put(e, updateData, {
        params: {
          ...params,
          id: updateData.id,
        },
        headers: updatedHeaders,
      })

      return res.data
    } catch (error) {
      console.error('更新資料時發生錯誤:', error)
      return {
        success: false,
        message: error.response?.data?.message || '更新失敗，請稍後再試',
      }
    }
  }

  const postApiData = async function (e, addData) {
    try {
      // 根據不同的API類型調整路徑
      let apiPath = e
      if (e === 'product') {
        apiPath = 'products'
      } else if (e === 'user') {
        apiPath = 'users/register'
        console.log('註冊請求路徑:', getApiUrl(apiPath))
      } else if (e === 'comments') {
        apiPath = 'basic/comments'
      }

      const res = await axiosInstance.post(apiPath, addData, {
        headers: getHeaders(),
      })

      if (res.data.success === false) {
        message.error(res.data.message || '新增失敗')
        return null
      }

      return res.data
    } catch (error) {
      console.error('Error in postApiData:', error)

      // 檢查是否為401錯誤
      if (error.response && error.response.status === 401) {
        handle401Error()
        return null
      }

      message.error(error.response?.data?.message || '新增失敗')
      return null
    }
  }

  const getUserToken = async function (record) {
    try {
      const res = await axiosInstance.post('user/login', {
        mail: record.account, // 將帳號映射為mail，適應後端API
        password: record.password,
      })
      return res.data.body.token
    } catch (error) {
      console.error('Error fetching client data:', error)
    }
  }

  const delApiData = async function (e, delData) {
    try {
      const params = { ...getParams() }
      // 根據不同的API類型調整路徑
      let apiPath = e
      if (e === 'product') {
        params.shop = localStorage.shop
        params.belongCp = ********
        params.userId = 'chengshe'
        apiPath = `${e}/del`
      } else if (e.startsWith('comments/')) {
        // 處理評論相關的API，例如 comments/123
        apiPath = `basic/${e}`
      } else {
        apiPath = `${e}/del`
      }

      // 處理 id 參數，確保正確格式
      let idParam = delData
      if (typeof delData === 'object' && delData.id) {
        idParam = delData.id
      }

      // 根據API類型決定如何發送請求
      let response
      if (e.startsWith('comments/')) {
        // 留言API使用DELETE方法，路徑包含ID
        console.log('發送留言刪除請求:', apiPath)
        response = await axiosInstance.delete(apiPath, {
          headers: getHeaders(),
        })
      } else {
        // 原有的API請求方式
        response = await axiosInstance.delete(apiPath, {
          params: {
            ...params,
            id: idParam,
            isUserDel: 0,
          },
          headers: getHeaders(),
        })
      }

      console.log('刪除結果:', response.data)
      return response.data || { success: true }
    } catch (error) {
      console.error('刪除API請求錯誤:', error)
      console.error('錯誤響應:', error.response?.data)
      return {
        success: false,
        status: error.response?.status || 500,
        message: error.response?.data?.message || error.response?.data?.error || '刪除失敗，請稍後再試',
      }
    }
  }
  const updateRead = async function (changData) {
    try {
      // 建立 params 和 headers 的複製，以免修改到原本的資料
      const params = { ...getParams() }
      await axiosInstance.patch('orderList/updateRead', changData, {
        params: {
          ...params,
          id: changData.id,
          isRead: 1,
        },
        headers: getHeaders(),
      })
      // console.log(changData)
    } catch (error) {
      console.error('Error fetching client data:', error)
    }
  }
  const cancelRequests = () => {
    cancelTokenSource.cancel('Operation canceled by the user.')
  }
  const checkHl = async function (i) {
    try {
      // await testtttt()

      console.log('checkHl', i)
      const res = await axiosInstance.get('hlList', {
        params: {
          ...getParams(),
          user: i.id,
        },
        headers: getHeaders(),
      })

      console.log('checkHl', res.data.body)

      const findHl = res.data.body.find((item) => item.isDelete == 0)
      console.log(findHl)
      // if (findHl.res != i.bonusPoints) {
      //   console.log('數字不符。覆蓋上去')
      //   await patchApiData('user', {
      //     id: i.id,
      //     userId: i.userId,
      //     bonusPoints: findHl.res
      //   })
      //   // storeApiData('user', { id: i.id })
      // } else {
      //   console.log('數字相符')
      // }
    } catch (error) {
      console.error('Error fetching client data:', error)
    }
  }

  // 使用管理員權限獲取數據
  const getAdminApiData = async function (e, i) {
    try {
      const adminLogin = localStorage.getItem('adminLogin')
      const adminToken = localStorage.getItem('adminToken')
      const adminInfo = JSON.parse(localStorage.getItem('adminInfo') || '{}')

      console.log('getAdminApiData請求:')
      console.log('- API路徑:', e)
      console.log('- 管理員登入狀態:', adminLogin)
      console.log('- 管理員令牌:', adminToken ? adminToken.substring(0, 10) + '...' : '未設置')
      console.log('- 管理員角色:', adminInfo.role)

      // 檢查登入狀態
      if (adminLogin !== 'true') {
        console.error('管理員API請求錯誤: 未登入或無效令牌')
        return { success: false, message: '您的管理員登入已過期，請重新登入' }
      }

      // 本機模式：如果沒有token但有adminLogin狀態，使用模擬數據
      const isLocalMode = adminLogin === 'true' && !adminToken

      if (isLocalMode) {
        console.log('使用本機模式，生成模擬數據')
        return await generateMockAdminData(e, i, adminInfo)
      }

      // 正常API模式：使用完整URL以避免相對路徑問題
      const fullUrl = getApiUrl(e)
      console.log('請求完整URL:', fullUrl)

      // 準備請求頭
      const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${adminToken}`,
      }

      // 發送真實的API請求
      const response = await axiosInstance.get(fullUrl, {
        params: i,
        headers: headers,
      })

      console.log('成功獲取數據:', e, response.data)
      return response.data
    } catch (error) {
      console.error('Admin API 請求錯誤:', error.message)
      console.error('錯誤詳情:', error.response?.data || error)

      return {
        success: false,
        message: error.response?.data?.message || '獲取數據失敗，請檢查您的管理員權限',
        errorCode: error.response?.status || 500,
      }
    }
  }

  // 生成模擬管理員數據
  const generateMockAdminData = async function (endpoint, params, adminInfo) {
    console.log('正在生成模擬數據:', endpoint)

    // 根據不同的API端點生成不同的模擬數據
    switch (endpoint) {
      case 'admin/dashboard':
        return {
          success: true,
          message: '獲取儀表板數據成功 (模擬數據)',
          stats: {
            totalProducts: 24,
            totalOrders: 18,
            newOrders: 3,
            totalSales: 52680,
            totalUsers: 10
          },
          recentOrders: Array(5).fill(null).map((_, i) => ({
            id: 100 + i,
            orderId: `ORD${100 + i}`,
            clientName: `測試客戶 ${i + 1}`,
            createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
            total: 1000 + Math.floor(Math.random() * 5000),
            status: Math.floor(Math.random() * 4) + 1
          })),
          adminInfo: {
            ...adminInfo,
            lastLogin: new Date().toISOString()
          }
        }

      case 'admin/products':
        // 模擬產品列表
        const mockProducts = Array(10).fill(null).map((_, i) => ({
          id: i + 1,
          name: `測試產品 ${i + 1}`,
          price: 100 + Math.floor(Math.random() * 900),
          sku: `SKU${1000 + i}`,
          description: `這是測試產品的描述 ${i + 1}`,
          images: ['/uploads/products/placeholder.jpg'],
          category: `測試分類 ${Math.floor(i/3) + 1}`,
          status: 'active',
          createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
        }))

        return {
          success: true,
          message: '獲取產品列表成功 (模擬數據)',
          products: mockProducts,
          total: 24
        }

      case 'admin/orders':
        // 模擬訂單列表
        const mockOrders = Array(params?.pageSize || 10).fill(null).map((_, i) => {
          const orderIndex = i + ((params?.page || 1) - 1) * (params?.pageSize || 10)
          return {
            id: 100 + orderIndex,
            orderId: `ORD${1000 + orderIndex}`,
            clientName: `測試客戶 ${orderIndex + 1}`,
            receiver: `收件人 ${orderIndex + 1}`,
            phone: `0912345${1000 + orderIndex}`.substring(0, 10),
            address: `測試地址 ${orderIndex + 1}`,
            total: 1000 + Math.floor(Math.random() * 5000),
            status: Math.floor(Math.random() * 4) + 1,
            createdAt: new Date(Date.now() - orderIndex * 24 * 60 * 60 * 1000).toISOString()
          }
        })

        return {
          success: true,
          message: '獲取訂單列表成功 (模擬數據)',
          orders: mockOrders,
          total: 38
        }

      case /^admin\/orders\/\d+$/.test(endpoint) ? endpoint : '':
        // 模擬訂單詳情
        const orderId = parseInt(endpoint.split('/').pop())

        return {
          success: true,
          message: '獲取訂單詳情成功 (模擬數據)',
          orderDetails: {
            id: orderId,
            orderId: `ORD${1000 + orderId}`,
            clientName: `測試客戶 ${orderId}`,
            receiver: `收件人 ${orderId}`,
            phone: `0912345${1000 + orderId}`.substring(0, 10),
            address: `測試地址 ${orderId}`,
            total: 1000 + Math.floor(Math.random() * 5000),
            status: Math.floor(Math.random() * 4) + 1,
            createdAt: new Date(Date.now() - orderId * 24 * 60 * 60 * 1000).toISOString(),
            products: [
              {
                id: 1,
                name: '測試產品 1',
                price: 350,
                quantity: 2,
                image: '/uploads/products/placeholder.jpg'
              },
              {
                id: 2,
                name: '測試產品 2',
                price: 480,
                quantity: 1,
                image: '/uploads/products/placeholder.jpg'
              }
            ]
          }
        }

      default:
        return {
          success: true,
          message: `模擬數據不支援此端點: ${endpoint}`,
          data: []
        }
    }
  }

  // 使用管理員權限更新數據 (PUT)
  const updateAdminApiData = async function (e, updateData) {
    try {
      const adminLogin = localStorage.getItem('adminLogin')
      const adminToken = localStorage.getItem('adminToken')

      // 檢查是否為本機模式
      const isLocalMode = adminLogin === 'true' && !adminToken

      if (isLocalMode) {
        console.log('本機模式 - 模擬更新數據:', e, updateData)

        // 模擬成功響應
        return {
          success: true,
          message: '數據更新成功 (模擬模式)',
          data: updateData
        }
      }

      const id = updateData.id
      const params = id ? { id } : {}

      // 使用完整URL以避免相對路徑問題
      const fullUrl = getApiUrl(e)
      console.log('發送PUT請求:', fullUrl, updateData)

      const res = await axiosInstance.put(fullUrl, updateData, {
        params,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${adminToken}`,
        },
      })

      console.log('PUT請求成功:', res.data)
      return res.data
    } catch (error) {
      console.error('Admin API 更新錯誤:', error.message)
      console.error('錯誤詳情:', error.response?.data || error)
      return {
        success: false,
        message: error.response?.data?.message || '更新失敗，請稍後再試',
      }
    }
  }

  // 使用管理員權限發送 PATCH 請求
  const patchAdminApiData = async function (e, patchData) {
    try {
      const adminLogin = localStorage.getItem('adminLogin')
      const adminToken = localStorage.getItem('adminToken')

      // 檢查是否為本機模式
      const isLocalMode = adminLogin === 'true' && !adminToken

      if (isLocalMode) {
        console.log('本機模式 - 模擬部分更新數據:', e, patchData)

        // 模擬成功響應
        return {
          success: true,
          message: '數據部分更新成功 (模擬模式)',
          data: patchData
        }
      }

      // 使用完整URL以避免相對路徑問題
      const fullUrl = getApiUrl(e)
      console.log('發送PATCH請求:', fullUrl, patchData)

      const res = await axiosInstance.patch(fullUrl, patchData, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${adminToken}`,
        },
      })

      console.log('PATCH請求成功:', res.data)
      return res.data
    } catch (error) {
      console.error('Admin API PATCH 錯誤:', error.message)
      console.error('錯誤詳情:', error.response?.data || error)
      return {
        success: false,
        message: error.response?.data?.message || '更新失敗，請稍後再試',
      }
    }
  }

  // 使用管理員權限發送數據
  const postAdminApiData = async function (e, addData) {
    try {
      const adminLogin = localStorage.getItem('adminLogin')
      const adminToken = localStorage.getItem('adminToken')

      // 檢查是否為本機模式
      const isLocalMode = adminLogin === 'true' && !adminToken

      if (isLocalMode) {
        console.log('本機模式 - 模擬新增數據:', e, addData)

        // 檢查是否是批量刪除訂單的請求
        if (e === 'admin/orders/batch-delete' && addData.ids) {
          return {
            success: true,
            message: `已成功刪除 ${addData.ids.length} 筆訂單 (模擬模式)`,
          }
        }

        // 一般請求的模擬響應
        return {
          success: true,
          message: '數據新增成功 (模擬模式)',
          data: {
            ...addData,
            id: Math.floor(Math.random() * 1000) + 1000
          }
        }
      }

      // 檢查是否為FormData類型
      const isFormData = addData instanceof FormData

      // 準備請求頭
      let headers = {
        Authorization: `Bearer ${adminToken}`,
      }

      // 只有在非FormData情況下添加Content-Type
      if (!isFormData) {
        headers['Content-Type'] = 'application/json'
      }

      // 使用完整URL以避免相對路徑問題
      const fullUrl = getApiUrl(e)
      console.log('正在發送POST請求到:', fullUrl)

      const res = await axiosInstance.post(fullUrl, addData, {
        headers: headers,
      })

      console.log('POST請求成功:', res.data)
      return res.data
    } catch (error) {
      console.error('Admin API 新增錯誤:', error.message)
      console.error('錯誤詳情:', error.response?.data || error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '新增失敗',
      }
    }
  }

  // 使用管理員權限刪除數據
  const deleteAdminApiData = async function (e, id) {
    try {
      const adminLogin = localStorage.getItem('adminLogin')
      const adminToken = localStorage.getItem('adminToken')

      // 檢查是否為本機模式
      const isLocalMode = adminLogin === 'true' && !adminToken

      if (isLocalMode) {
        console.log('本機模式 - 模擬刪除數據:', e, id)

        // 模擬成功響應
        return {
          success: true,
          message: '數據刪除成功 (模擬模式)',
        }
      }

      // 處理帶有查詢參數的 URL，例如 "admin/products?id=6"
      let url = e
      let params = {}

      // 如果提供了 ID 參數
      if (id !== undefined) {
        params.id = id
      } else if (typeof e === 'string' && e.includes('?')) {
        // 從 URL 中提取查詢參數
        const [path, queryString] = e.split('?')
        url = path

        // 解析查詢字符串
        const searchParams = new URLSearchParams(queryString)
        for (const [key, value] of searchParams.entries()) {
          params[key] = value
        }
      }

      // 使用完整URL以避免相對路徑問題
      const fullUrl = getApiUrl(url)
      console.log('發送DELETE請求:', fullUrl, params)

      // 發送請求
      const res = await axiosInstance.delete(fullUrl, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${adminToken}`,
        },
        data: params, // 在 DELETE 請求中包含請求體
      })

      console.log('DELETE請求成功:', res.data)
      return res.data
    } catch (error) {
      console.error('Admin API 刪除錯誤:', error.message)
      console.error('錯誤詳情:', error.response?.data || error)
      return {
        success: false,
        message: error.response?.data?.message || '刪除失敗',
      }
    }
  }

  // 檢查請求頭是否有有效的身份驗證令牌
  const checkAuthToken = () => {
    const token = localStorage.getItem('token')
    if (!token || token === 'undefined' || token === 'null') {
      console.error('無效的認證令牌:', token)
      return false
    }
    return true
  }

  const logApiError = (apiName, error) => {
    console.error(`API 錯誤 [${apiName}]:`, error)
    if (error.response) {
      console.error('響應狀態:', error.response.status)
      console.error('響應數據:', error.response.data)
    } else if (error.request) {
      console.error('請求發送但無響應')
    } else {
      console.error('錯誤信息:', error.message)
    }
  }

  // 生成模擬商品詳情資料
  const generateMockProductDetail = function (productId) {
    const mockProduct = {
      id: productId,
      name: `測試商品 ${productId} - 富文本顏色測試`,
      description: `
        <h1>商品說明</h1>
        <p style="margin-left:0px;">
          <span style="background-color:hsl(0, 75%, 60%);color:hsl(180, 75%, 60%);">
            <strong>正宗秘魯國寶 彩紅馬卡MACA</strong>
          </span>
        </p>
        <p>這是一個測試商品，用於驗證富文本編輯器的顏色顯示功能。</p>
        <p>
          <span style="color:hsl(120, 75%, 60%);">綠色文字測試</span> -
          <span style="color:hsl(240, 75%, 60%);">藍色文字測試</span> -
          <span style="color:rgb(255, 0, 0);">紅色文字測試</span>
        </p>
        <p>
          <span style="background-color:hsl(120, 75%, 60%);color:#ffffff;">綠色背景白色文字</span>
        </p>
      `,
      highlights: `
        <p style="margin-left:0px;">
          <span style="background-color:hsl(0, 75%, 60%);color:hsl(180, 75%, 60%);">
            <strong>▲ 強力上市</strong>
          </span>
        </p>
        <p style="margin-left:0px;">
          <span style="color:hsl(120, 75%, 60%);">
            <strong>⭐ 安地斯山脈 優質10倍濃縮馬卡</strong>
          </span>
        </p>
        <p style="margin-left:0px;">
          <span style="color:hsl(240, 75%, 60%);">
            <strong>⭐ enXtra 專利南薑萃取物</strong>
          </span>
        </p>
      `,
      desc2: `
        <h2>商品規格</h2>
        <p>
          <span style="color:rgb(255, 0, 0);">重要規格資訊</span>
        </p>
        <ul>
          <li><span style="color:hsl(194, 70%, 21%);">容量：500ml</span></li>
          <li><span style="color:hsl(216, 5%, 32%);">重量：1kg</span></li>
        </ul>
      `,
      price1: 1000 + (productId * 100),
      price2: 800 + (productId * 80),
      stock: 50,
      images: [
        '/uploads/products/placeholder.jpg',
        '/uploads/products/placeholder2.jpg'
      ],
      sort: '測試分類',
      isDelete: 0,
      is_featured: productId <= 3 ? 1 : 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    return {
      success: true,
      body: mockProduct,
      resData: [mockProduct],
      product: mockProduct
    }
  }

  // 新增：專門獲取單個商品詳情的函數
  const getProductById = async function (id) {
    try {
      if (!id) {
        throw new Error('商品ID為必填項')
      }
      
      const res = await axios.get(getApiUrl(`products/${id}`), {
        headers: getHeaders(),
        cancelToken: cancelTokenSource.token,
      })

      if (!res || !res.data) {
        throw new Error('API響應無效')
      }

      if (!res.data.success) {
        throw new Error(res.data.message || '獲取商品失敗')
      }
      
      // 處理返回的商品數據
      let product = res.data.body
      
      if (!product) {
        throw new Error('商品不存在')
      }

      // 處理圖片數據
      if (product.images && typeof product.images === 'string') {
        try {
          product.images = JSON.parse(product.images)
        } catch (e) {
          console.warn('圖片數據解析失敗，使用原始數據:', product.images)
          product.images = [product.images]
        }
      } else if (!Array.isArray(product.images)) {
        product.images = []
      }

      return {
        success: true,
        resData: [product], // 包裝成數組格式以保持兼容性
        product: product
      }
    } catch (error) {
      console.error('💥 獲取商品詳情失敗:', error)
      
      if (error.response && error.response.status === 404) {
        throw new Error('商品不存在或已被刪除')
      }
      
      throw new Error(error.message || '獲取商品詳情失敗，請稍後再試')
    }
  }

  return {
    getApiData,
    patchApiData,
    postApiData,
    delApiData,
    getUserToken,
    cancelRequests,
    updateRead,
    checkHl,
    updateApiData,
    getAdminApiData,
    updateAdminApiData,
    patchAdminApiData,
    postAdminApiData,
    deleteAdminApiData,
    // 使用導入的 getImageUrl 函數
    getImageUrl,
    checkAuthToken,
    logApiError,
    getProductById
  }
}
