/* 重點商品資訊專用樣式 */

/* 邊框樣式 */
.des.border-bottom {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

/* 基礎容器樣式 */
.highlights-content {
  text-align: left;
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "微軟正黑體", "Microsoft JhengHei", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 重要：讓內聯樣式優先生效 */
.highlights-content * {
  /* 不使用 all: revert，避免破壞內聯樣式 */
  margin: 0;
  padding: 0;
}

/* 基礎樣式：不強制覆蓋顏色，讓內聯樣式生效 */
.highlights-content {
  /* 設定基礎顏色，但不強制覆蓋子元素 */
  color: #333;
}

/* 只對沒有內聯樣式的元素設定預設顏色 */
.highlights-content span:not([style*="color"]),
.highlights-content h1:not([style*="color"]),
.highlights-content h2:not([style*="color"]),
.highlights-content h3:not([style*="color"]),
.highlights-content h4:not([style*="color"]),
.highlights-content h5:not([style*="color"]),
.highlights-content h6:not([style*="color"]),
.highlights-content p:not([style*="color"]),
.highlights-content div:not([style*="color"]),
.highlights-content strong:not([style*="color"]),
.highlights-content b:not([style*="color"]) {
  color: inherit;
}

/* 段落樣式 */
.highlights-content p {
  margin: 0.5rem 0;
  line-height: inherit;
}

/* 標題基礎樣式 */
.highlights-content h1,
.highlights-content h2,
.highlights-content h3,
.highlights-content h4,
.highlights-content h5,
.highlights-content h6 {
  margin: 0.5rem 0;
  line-height: 1.3;
  font-weight: normal; /* 讓 strong 標籤控制粗體 */
}

/* 確保 span 內聯樣式正確顯示 */
.highlights-content span {
  display: inline;
  line-height: inherit;
}

/* 強制內聯樣式生效 - 字體大小 */
.highlights-content [style*="font-size:48px"] {
  font-size: 48px !important;
}

.highlights-content [style*="font-size:20px"] {
  font-size: 20px !important;
}

.highlights-content [style*="font-size:14px"] {
  font-size: 14px !important;
}

/* 強制內聯樣式生效 - 顏色（包含所有可能的格式） */
/* 使用最高優先級選擇器覆蓋 Tailwind 的 important 設定 */
html .highlights-content [style*="color:rgb(255,0,0)"],
html .highlights-content [style*="color: rgb(255, 0, 0)"],
html .highlights-content [style*="color:hsl(0, 75%, 60%)"],
html .highlights-content [style*="color: hsl(0, 75%, 60%)"],
html .highlights-content [style*="color:hsl(0,75%,60%)"],
html .highlights-content [style*="color: hsl(0,75%,60%)"],
html body .highlights-content [style*="color:rgb(255,0,0)"],
html body .highlights-content [style*="color: rgb(255, 0, 0)"],
html body .highlights-content [style*="color:hsl(0, 75%, 60%)"],
html body .highlights-content [style*="color: hsl(0, 75%, 60%)"],
html body .highlights-content [style*="color:hsl(0,75%,60%)"],
html body .highlights-content [style*="color: hsl(0,75%,60%)"] {
  color: rgb(255,0,0) !important;
}

html .highlights-content [style*="color:rgb(16,74,90)"],
html .highlights-content [style*="color: rgb(16, 74, 90)"],
html .highlights-content [style*="color:hsl(194,70%,21%)"],
html .highlights-content [style*="color: hsl(194,70%,21%)"],
html .highlights-content [style*="color:hsl(194, 70%, 21%)"],
html .highlights-content [style*="color: hsl(194, 70%, 21%)"],
html body .highlights-content [style*="color:rgb(16,74,90)"],
html body .highlights-content [style*="color: rgb(16, 74, 90)"],
html body .highlights-content [style*="color:hsl(194,70%,21%)"],
html body .highlights-content [style*="color: hsl(194,70%,21%)"],
html body .highlights-content [style*="color:hsl(194, 70%, 21%)"],
html body .highlights-content [style*="color: hsl(194, 70%, 21%)"] {
  color: rgb(16,74,90) !important;
}

html .highlights-content [style*="color:rgb(77,81,86)"],
html .highlights-content [style*="color: rgb(77, 81, 86)"],
html .highlights-content [style*="color:hsl(216,5%,32%)"],
html .highlights-content [style*="color: hsl(216,5%,32%)"],
html .highlights-content [style*="color:hsl(216, 5%, 32%)"],
html .highlights-content [style*="color: hsl(216, 5%, 32%)"],
html body .highlights-content [style*="color:rgb(77,81,86)"],
html body .highlights-content [style*="color: rgb(77, 81, 86)"],
html body .highlights-content [style*="color:hsl(216,5%,32%)"],
html body .highlights-content [style*="color: hsl(216,5%,32%)"],
html body .highlights-content [style*="color:hsl(216, 5%, 32%)"],
html body .highlights-content [style*="color: hsl(216, 5%, 32%)"] {
  color: rgb(77,81,86) !important;
}

html .highlights-content [style*="color:#4d5156"],
html body .highlights-content [style*="color:#4d5156"] {
  color: #4d5156 !important;
}

/* 更強制的顏色覆蓋 - 使用更高優先級 */
/* 覆蓋 scoped 樣式 */
[data-v-2b7a8c79] .highlights-content span[style*="color:hsl(0"],
[data-v-2b7a8c79] .highlights-content span[style*="color: hsl(0"],
.highlights-content[data-v-2b7a8c79] span[style*="color:hsl(0"],
.highlights-content[data-v-2b7a8c79] span[style*="color: hsl(0"] {
  color: rgb(255,0,0) !important;
}

[data-v-2b7a8c79] .highlights-content span[style*="color:hsl(194"],
[data-v-2b7a8c79] .highlights-content span[style*="color: hsl(194"],
.highlights-content[data-v-2b7a8c79] span[style*="color:hsl(194"],
.highlights-content[data-v-2b7a8c79] span[style*="color: hsl(194"] {
  color: rgb(16,74,90) !important;
}

[data-v-2b7a8c79] .highlights-content span[style*="color:hsl(216"],
[data-v-2b7a8c79] .highlights-content span[style*="color: hsl(216"],
.highlights-content[data-v-2b7a8c79] span[style*="color:hsl(216"],
.highlights-content[data-v-2b7a8c79] span[style*="color: hsl(216"] {
  color: rgb(77,81,86) !important;
}

/* 確保所有標題內的紅色文字正確顯示 */
.highlights-content h1 span[style*="color:hsl(0"],
.highlights-content h2 span[style*="color:hsl(0"],
.highlights-content h3 span[style*="color:hsl(0"],
.highlights-content h4 span[style*="color:hsl(0"] {
  color: rgb(255,0,0) !important;
}

/* 使用屬性選擇器強制覆蓋 */
.highlights-content [style*="color: hsl(0, 75%, 60%)"] {
  color: rgb(255,0,0) !important;
}

.highlights-content [style*="color: hsl(194, 70%, 21%)"] {
  color: rgb(16,74,90) !important;
}

.highlights-content [style*="color: hsl(216, 5%, 32%)"] {
  color: rgb(77,81,86) !important;
}

/* 使用 data 屬性強制顏色 - 最高優先級 */
.highlights-content [data-color="red"],
.highlights-content[data-v-2b7a8c79] [data-color="red"],
[data-v-2b7a8c79] .highlights-content [data-color="red"] {
  color: rgb(255, 0, 0) !important;
}

.highlights-content [data-color="blue"],
.highlights-content[data-v-2b7a8c79] [data-color="blue"],
[data-v-2b7a8c79] .highlights-content [data-color="blue"] {
  color: rgb(16, 74, 90) !important;
}

.highlights-content [data-color="gray"],
.highlights-content[data-v-2b7a8c79] [data-color="gray"],
[data-v-2b7a8c79] .highlights-content [data-color="gray"] {
  color: rgb(77, 81, 86) !important;
}

/* 確保內聯樣式的 RGB 顏色正確顯示 */
.highlights-content [style*="color: rgb(255, 0, 0)"],
.highlights-content[data-v-2b7a8c79] [style*="color: rgb(255, 0, 0)"] {
  color: rgb(255, 0, 0) !important;
}

.highlights-content [style*="color: rgb(16, 74, 90)"],
.highlights-content[data-v-2b7a8c79] [style*="color: rgb(16, 74, 90)"] {
  color: rgb(16, 74, 90) !important;
}

.highlights-content [style*="color: rgb(77, 81, 86)"],
.highlights-content[data-v-2b7a8c79] [style*="color: rgb(77, 81, 86)"] {
  color: rgb(77, 81, 86) !important;
}

/* 使用 class 強制顏色 - 終極解決方案 */
/* 使用最高優先級選擇器覆蓋所有可能的樣式 */
html body .highlights-content .highlight-red,
html body .highlights-content[data-v-2b7a8c79] .highlight-red,
html body [data-v-2b7a8c79] .highlights-content .highlight-red,
html body .highlight-red,
html .highlights-content .highlight-red,
html .highlights-content[data-v-2b7a8c79] .highlight-red,
html [data-v-2b7a8c79] .highlights-content .highlight-red,
html .highlight-red,
.highlights-content .highlight-red,
.highlights-content[data-v-2b7a8c79] .highlight-red,
[data-v-2b7a8c79] .highlights-content .highlight-red,
.highlight-red {
  color: rgb(255, 0, 0) !important;
}

html body .highlights-content .highlight-blue,
html body .highlights-content[data-v-2b7a8c79] .highlight-blue,
html body [data-v-2b7a8c79] .highlights-content .highlight-blue,
html body .highlight-blue,
html .highlights-content .highlight-blue,
html .highlights-content[data-v-2b7a8c79] .highlight-blue,
html [data-v-2b7a8c79] .highlights-content .highlight-blue,
html .highlight-blue,
.highlights-content .highlight-blue,
.highlights-content[data-v-2b7a8c79] .highlight-blue,
[data-v-2b7a8c79] .highlights-content .highlight-blue,
.highlight-blue {
  color: rgb(16, 74, 90) !important;
}

html body .highlights-content .highlight-gray,
html body .highlights-content[data-v-2b7a8c79] .highlight-gray,
html body [data-v-2b7a8c79] .highlights-content .highlight-gray,
html body .highlight-gray,
html .highlights-content .highlight-gray,
html .highlights-content[data-v-2b7a8c79] .highlight-gray,
html [data-v-2b7a8c79] .highlights-content .highlight-gray,
html .highlight-gray,
.highlights-content .highlight-gray,
.highlights-content[data-v-2b7a8c79] .highlight-gray,
[data-v-2b7a8c79] .highlights-content .highlight-gray,
.highlight-gray {
  color: rgb(77, 81, 86) !important;
}

html body .highlights-content .highlight-green,
html body .highlights-content[data-v-2b7a8c79] .highlight-green,
html body [data-v-2b7a8c79] .highlights-content .highlight-green,
html body .highlight-green,
html .highlights-content .highlight-green,
html .highlights-content[data-v-2b7a8c79] .highlight-green,
html [data-v-2b7a8c79] .highlights-content .highlight-green,
html .highlight-green,
.highlights-content .highlight-green,
.highlights-content[data-v-2b7a8c79] .highlight-green,
[data-v-2b7a8c79] .highlights-content .highlight-green,
.highlight-green {
  color: hsl(120, 75%, 60%) !important;
}

/* 針對所有子元素強制顏色 */
.highlight-red *,
.highlight-blue *,
.highlight-gray * {
  color: inherit !important;
}

/* 強制內聯樣式生效 - 字體 */
.highlights-content [style*="font-family:Tahoma"],
.highlights-content [style*="font-family: Tahoma"] {
  font-family: Tahoma, Geneva, sans-serif !important;
}

.highlights-content [style*="font-family:Arial"],
.highlights-content [style*="font-family: Arial"] {
  font-family: Arial, Helvetica, sans-serif !important;
}

/* 確保粗體顯示 */
.highlights-content strong,
.highlights-content b {
  font-weight: bold !important;
}

/* 處理空白字符 */
.highlights-content span {
  white-space: pre-wrap;
}

/* 特殊處理 margin */
.highlights-content [style*="margin-left:0px"],
.highlights-content [style*="margin-left: 0px"] {
  margin-left: 0 !important;
}

/* 列表樣式 */
.highlights-content ul,
.highlights-content ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.highlights-content li {
  margin: 0.25rem 0;
}

/* 確保標題內的元素正確顯示 */
.highlights-content h1 span,
.highlights-content h2 span,
.highlights-content h3 span,
.highlights-content h4 span,
.highlights-content h5 span,
.highlights-content h6 span {
  display: inline !important;
}

/* 連結樣式 */
.highlights-content a {
  color: #0066cc;
  text-decoration: underline;
}

.highlights-content a:hover {
  color: #0052a3;
}

/* 表格樣式 */
.highlights-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.highlights-content table td,
.highlights-content table th {
  border: 1px solid #ddd;
  padding: 8px;
}

/* 引用樣式 */
.highlights-content blockquote {
  border-left: 4px solid #ccc;
  margin: 1rem 0;
  padding-left: 1rem;
  font-style: italic;
}

/* 防止內容溢出 */
.highlights-content * {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 響應式調整 */
@media (max-width: 768px) {
  .highlights-content [style*="font-size:48px"] {
    font-size: 36px !important;
  }

  .highlights-content [style*="font-size:20px"] {
    font-size: 18px !important;
  }
}

/* 終極解決方案：使用最高優先級覆蓋所有可能的全域樣式 */
/* 針對特定的 HSL 顏色值進行強制轉換 */
html body div.highlights-content span[style*="color:hsl(120, 75%, 60%)"],
html body div.highlights-content span[style*="color: hsl(120, 75%, 60%)"],
html body .highlights-content span[style*="color:hsl(120, 75%, 60%)"],
html body .highlights-content span[style*="color: hsl(120, 75%, 60%)"],
html div.highlights-content span[style*="color:hsl(120, 75%, 60%)"],
html div.highlights-content span[style*="color: hsl(120, 75%, 60%)"],
html .highlights-content span[style*="color:hsl(120, 75%, 60%)"],
html .highlights-content span[style*="color: hsl(120, 75%, 60%)"] {
  color: hsl(120, 75%, 60%) !important;
}

html body div.highlights-content span[style*="color:hsl(240, 75%, 60%)"],
html body div.highlights-content span[style*="color: hsl(240, 75%, 60%)"],
html body .highlights-content span[style*="color:hsl(240, 75%, 60%)"],
html body .highlights-content span[style*="color: hsl(240, 75%, 60%)"],
html div.highlights-content span[style*="color:hsl(240, 75%, 60%)"],
html div.highlights-content span[style*="color: hsl(240, 75%, 60%)"],
html .highlights-content span[style*="color:hsl(240, 75%, 60%)"],
html .highlights-content span[style*="color: hsl(240, 75%, 60%)"] {
  color: hsl(240, 75%, 60%) !important;
}

/* 新增綠色支援 */
html body div.highlights-content span[style*="color:hsl(120, 75%, 60%)"],
html body div.highlights-content span[style*="color: hsl(120, 75%, 60%)"],
html body .highlights-content span[style*="color:hsl(120, 75%, 60%)"],
html body .highlights-content span[style*="color: hsl(120, 75%, 60%)"],
html div.highlights-content span[style*="color:hsl(120, 75%, 60%)"],
html div.highlights-content span[style*="color: hsl(120, 75%, 60%)"],
html .highlights-content span[style*="color:hsl(120, 75%, 60%)"],
html .highlights-content span[style*="color: hsl(120, 75%, 60%)"] {
  color: hsl(120, 75%, 60%) !important;
}

html body div.highlights-content span[style*="color:rgb(255,0,0)"],
html body div.highlights-content span[style*="color: rgb(255, 0, 0)"],
html body .highlights-content span[style*="color:rgb(255,0,0)"],
html body .highlights-content span[style*="color: rgb(255, 0, 0)"],
html div.highlights-content span[style*="color:rgb(255,0,0)"],
html div.highlights-content span[style*="color: rgb(255, 0, 0)"],
html .highlights-content span[style*="color:rgb(255,0,0)"],
html .highlights-content span[style*="color: rgb(255, 0, 0)"] {
  color: rgb(255, 0, 0) !important;
}

/* 確保內聯樣式能夠正確顯示 - 移除強制 inherit */
html body .highlights-content [style*="color:"],
html .highlights-content [style*="color:"] {
  /* 不強制覆蓋，讓內聯樣式自然生效 */
}

/* 確保所有帶有 style 屬性的元素都能正確顯示顏色 */
html body .highlights-content *[style],
html .highlights-content *[style] {
  /* 重置可能影響顏色的屬性 */
  text-decoration-color: inherit !important;
  border-color: inherit !important;
}