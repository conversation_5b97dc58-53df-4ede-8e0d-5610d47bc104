/*
 * 內聯樣式修復 CSS
 * 專門用於解決 Tailwind CSS important 覆蓋內聯樣式的問題
 */

/* 專門針對富文本內容的強制內聯樣式類別 */
.force-inline-styles {
  /* 設定基礎樣式，但不覆蓋內聯樣式 */
  line-height: 1.8;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 重要：不設定任何會覆蓋內聯樣式的屬性 */
.force-inline-styles *[style] {
  /* 這個選擇器存在是為了提高優先級，但不設定任何屬性 */
  /* 讓內聯樣式自然生效 */
}

/* 確保基本的顯示屬性正確 */
.force-inline-styles span[style] {
  display: inline !important;
}

.force-inline-styles p[style] {
  display: block !important;
  margin: 1em 0 !important;
}

.force-inline-styles strong[style],
.force-inline-styles b[style] {
  font-weight: bold !important;
}

.force-inline-styles em[style],
.force-inline-styles i[style] {
  font-style: italic !important;
}

.force-inline-styles h1[style] {
  display: block !important;
  font-size: 2em !important;
  font-weight: bold !important;
  margin: 0.67em 0 !important;
}

.force-inline-styles h2[style] {
  display: block !important;
  font-size: 1.5em !important;
  font-weight: bold !important;
  margin: 0.83em 0 !important;
}

.force-inline-styles h3[style] {
  display: block !important;
  font-size: 1.17em !important;
  font-weight: bold !important;
  margin: 1em 0 !important;
}

.force-inline-styles ul[style] {
  display: block !important;
  list-style-type: disc !important;
  margin: 1em 0 !important;
  padding-left: 40px !important;
}

.force-inline-styles ol[style] {
  display: block !important;
  list-style-type: decimal !important;
  margin: 1em 0 !important;
  padding-left: 40px !important;
}

.force-inline-styles li[style] {
  display: list-item !important;
}

/* 
 * 使用最高優先級的選擇器來確保內聯樣式能夠正確顯示
 * 這些規則會覆蓋 Tailwind 的 !important 設定
 */

/* 基礎內聯樣式保護 - 使用極高優先級覆蓋 Tailwind 的 important 設定 */
html body #app div.rich-text-content *[style*="color:"],
html body #app div.highlights-content *[style*="color:"],
html body #app .rich-text-content *[style*="color:"],
html body #app .highlights-content *[style*="color:"],
html body div.rich-text-content *[style*="color:"],
html body div.highlights-content *[style*="color:"],
html body .rich-text-content *[style*="color:"],
html body .highlights-content *[style*="color:"] {
  /* 不設定任何會覆蓋內聯樣式的屬性，讓內聯樣式自然生效 */
}

html body div.rich-text-content *[style*="background-color:"],
html body div.highlights-content *[style*="background-color:"],
html body .rich-text-content *[style*="background-color:"],
html body .highlights-content *[style*="background-color:"] {
  /* 不設定任何會覆蓋內聯樣式的屬性，讓內聯樣式自然生效 */
}

/* 針對特定的 HSL 和 RGB 顏色值使用精確匹配 */
html body .rich-text-content *[style*="color:hsl(0, 75%, 60%)"],
html body .rich-text-content *[style*="color: hsl(0, 75%, 60%)"],
html body .highlights-content *[style*="color:hsl(0, 75%, 60%)"],
html body .highlights-content *[style*="color: hsl(0, 75%, 60%)"] {
  color: hsl(0, 75%, 60%) !important;
}

html body .rich-text-content *[style*="color:hsl(180, 75%, 60%)"],
html body .rich-text-content *[style*="color: hsl(180, 75%, 60%)"],
html body .highlights-content *[style*="color:hsl(180, 75%, 60%)"],
html body .highlights-content *[style*="color: hsl(180, 75%, 60%)"] {
  color: hsl(180, 75%, 60%) !important;
}

html body .rich-text-content *[style*="color:hsl(120, 75%, 60%)"],
html body .rich-text-content *[style*="color: hsl(120, 75%, 60%)"],
html body .highlights-content *[style*="color:hsl(120, 75%, 60%)"],
html body .highlights-content *[style*="color: hsl(120, 75%, 60%)"] {
  color: hsl(120, 75%, 60%) !important;
}

html body .rich-text-content *[style*="color:hsl(240, 75%, 60%)"],
html body .rich-text-content *[style*="color: hsl(240, 75%, 60%)"],
html body .highlights-content *[style*="color:hsl(240, 75%, 60%)"],
html body .highlights-content *[style*="color: hsl(240, 75%, 60%)"] {
  color: hsl(240, 75%, 60%) !important;
}

html body .rich-text-content *[style*="color:rgb(255, 0, 0)"],
html body .rich-text-content *[style*="color: rgb(255, 0, 0)"],
html body .highlights-content *[style*="color:rgb(255, 0, 0)"],
html body .highlights-content *[style*="color: rgb(255, 0, 0)"] {
  color: rgb(255, 0, 0) !important;
}

html body .rich-text-content *[style*="color:rgb(0, 255, 255)"],
html body .rich-text-content *[style*="color: rgb(0, 255, 255)"],
html body .highlights-content *[style*="color:rgb(0, 255, 255)"],
html body .highlights-content *[style*="color: rgb(0, 255, 255)"] {
  color: rgb(0, 255, 255) !important;
}

/* 背景色的精確匹配 */
html body .rich-text-content *[style*="background-color:hsl(0, 75%, 60%)"],
html body .rich-text-content *[style*="background-color: hsl(0, 75%, 60%)"],
html body .highlights-content *[style*="background-color:hsl(0, 75%, 60%)"],
html body .highlights-content *[style*="background-color: hsl(0, 75%, 60%)"] {
  background-color: hsl(0, 75%, 60%) !important;
}

html body .rich-text-content *[style*="background-color:hsl(180, 75%, 60%)"],
html body .rich-text-content *[style*="background-color: hsl(180, 75%, 60%)"],
html body .highlights-content *[style*="background-color:hsl(180, 75%, 60%)"],
html body .highlights-content *[style*="background-color: hsl(180, 75%, 60%)"] {
  background-color: hsl(180, 75%, 60%) !important;
}

html body .rich-text-content *[style*="background-color:hsl(120, 75%, 60%)"],
html body .rich-text-content *[style*="background-color: hsl(120, 75%, 60%)"],
html body .highlights-content *[style*="background-color:hsl(120, 75%, 60%)"],
html body .highlights-content *[style*="background-color: hsl(120, 75%, 60%)"] {
  background-color: hsl(120, 75%, 60%) !important;
}

html body .rich-text-content *[style*="background-color:hsl(240, 75%, 60%)"],
html body .rich-text-content *[style*="background-color: hsl(240, 75%, 60%)"],
html body .highlights-content *[style*="background-color:hsl(240, 75%, 60%)"],
html body .highlights-content *[style*="background-color: hsl(240, 75%, 60%)"] {
  background-color: hsl(240, 75%, 60%) !important;
}

html body .rich-text-content *[style*="background-color:rgb(255, 0, 0)"],
html body .rich-text-content *[style*="background-color: rgb(255, 0, 0)"],
html body .highlights-content *[style*="background-color:rgb(255, 0, 0)"],
html body .highlights-content *[style*="background-color: rgb(255, 0, 0)"] {
  background-color: rgb(255, 0, 0) !important;
}

html body .rich-text-content *[style*="background-color:rgb(0, 255, 255)"],
html body .rich-text-content *[style*="background-color: rgb(0, 255, 255)"],
html body .highlights-content *[style*="background-color:rgb(0, 255, 255)"],
html body .highlights-content *[style*="background-color: rgb(0, 255, 255)"] {
  background-color: rgb(0, 255, 255) !important;
}

/* 字體大小的內聯樣式保護 */
html body .rich-text-content *[style*="font-size:"],
html body .highlights-content *[style*="font-size:"] {
  /* 讓內聯樣式自然生效 */
}

/* 字體家族的內聯樣式保護 */
html body .rich-text-content *[style*="font-family:"],
html body .highlights-content *[style*="font-family:"] {
  /* 讓內聯樣式自然生效 */
}

/* 文字對齊的內聯樣式保護 */
html body .rich-text-content *[style*="text-align:"],
html body .highlights-content *[style*="text-align:"] {
  /* 讓內聯樣式自然生效 */
}

/*
 * 終極解決方案：使用最高優先級覆蓋所有可能的 Tailwind 樣式
 * 針對 #app 內部的元素使用更高的優先級
 */
html body #app .rich-text-content *[style],
html body #app .highlights-content *[style],
html body .rich-text-content *[style],
html body .highlights-content *[style] {
  /* 確保內聯樣式具有最高優先級 */
  all: revert;
}

/* 針對可能被 Tailwind 覆蓋的文字顏色，使用極高優先級但不覆蓋內聯樣式 */
html body #app .rich-text-content *[style*="color:"],
html body #app .rich-text-content *[style*="background-color:"] {
  /* 不設定任何會覆蓋內聯樣式的屬性 */
  /* 這個選擇器的存在是為了提高優先級，讓內聯樣式能夠生效 */
}

/* 恢復基本的顯示屬性 */
.rich-text-content span[style] {
  display: inline;
}

.rich-text-content p[style] {
  display: block;
  margin: 1em 0;
}

.rich-text-content strong[style],
.rich-text-content b[style] {
  font-weight: bold;
}

.rich-text-content em[style],
.rich-text-content i[style] {
  font-style: italic;
}

.rich-text-content h1[style] {
  display: block;
  font-size: 2em;
  font-weight: bold;
  margin: 0.67em 0;
}

.rich-text-content h2[style] {
  display: block;
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.83em 0;
}

.rich-text-content h3[style] {
  display: block;
  font-size: 1.17em;
  font-weight: bold;
  margin: 1em 0;
}

/* 確保列表樣式正確 */
.rich-text-content ul[style] {
  display: block;
  list-style-type: disc;
  margin: 1em 0;
  padding-left: 40px;
}

.rich-text-content ol[style] {
  display: block;
  list-style-type: decimal;
  margin: 1em 0;
  padding-left: 40px;
}

.rich-text-content li[style] {
  display: list-item;
}

/* 確保表格樣式正確 */
.rich-text-content table[style] {
  display: table;
  border-collapse: separate;
  border-spacing: 2px;
}

.rich-text-content tr[style] {
  display: table-row;
}

.rich-text-content td[style],
.rich-text-content th[style] {
  display: table-cell;
  padding: 1px;
}

/* 確保連結樣式正確 */
.rich-text-content a[style] {
  color: #0000EE;
  text-decoration: underline;
}

.rich-text-content a[style]:visited {
  color: #551A8B;
}
