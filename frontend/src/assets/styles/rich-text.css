/* 富文本內容樣式 - 前端顯示用 */
.rich-text-content {
  line-height: 1.8;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 基本文字樣式 */
.rich-text-content h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
}

.rich-text-content h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
}

.rich-text-content h3 {
  font-size: 1.17em;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
}

.rich-text-content p {
  margin: 1em 0;
}

.rich-text-content strong,
.rich-text-content b {
  font-weight: bold;
}

.rich-text-content em,
.rich-text-content i {
  font-style: italic;
}

.rich-text-content u {
  text-decoration: underline;
}

.rich-text-content s {
  text-decoration: line-through;
}

/* 列表樣式 */
.rich-text-content ul,
.rich-text-content ol {
  padding-left: 2em;
  margin: 1em 0;
}

.rich-text-content ul {
  list-style-type: disc;
}

.rich-text-content ol {
  list-style-type: decimal;
}

.rich-text-content li {
  margin: 0.5em 0;
}

/* 表格樣式 */
.rich-text-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.rich-text-content th,
.rich-text-content td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.rich-text-content th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.rich-text-content tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* 引用樣式 */
.rich-text-content blockquote {
  border-left: 4px solid #ddd;
  margin: 1em 0;
  padding-left: 1em;
  color: #666;
  font-style: italic;
}

/* 代碼樣式 */
.rich-text-content code {
  font-family: 'Courier New', Courier, monospace;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
}

.rich-text-content pre {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1em;
  overflow-x: auto;
  margin: 1em 0;
}

.rich-text-content pre code {
  background-color: transparent;
  padding: 0;
}

/* 分隔線樣式 */
.rich-text-content hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 2em 0;
}

/* 鏈接樣式 */
.rich-text-content a {
  color: #0066cc;
  text-decoration: underline;
}

.rich-text-content a:hover {
  color: #004499;
  text-decoration: underline;
}

/* 圖片樣式 - 重要！確保圖片置中正確顯示 */
.rich-text-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
}

/* CKEditor 5 圖片對齊樣式 */
.rich-text-content .image {
  display: table;
  clear: both;
  text-align: center;
  margin: 0.9em auto;
}

.rich-text-content .image img {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  min-width: 50px;
}

/* 圖片置中樣式 - 支援多種編輯器格式 */
.rich-text-content .image-style-align-center,
.rich-text-content .image-style-center,
.rich-text-content figure.image {
  margin-left: auto !important;
  margin-right: auto !important;
  text-align: center !important;
}

.rich-text-content .image-style-align-center img,
.rich-text-content .image-style-center img,
.rich-text-content figure.image img {
  margin: 0 auto !important;
  display: block !important;
}

/* 圖片靠左樣式 */
.rich-text-content .image-style-align-left,
.rich-text-content .image-style-side {
  float: left;
  margin-right: 1.5em;
  margin-left: 0;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

/* 圖片靠右樣式 */
.rich-text-content .image-style-align-right {
  float: right;
  margin-left: 1.5em;
  margin-right: 0;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

/* 圖片說明文字 */
.rich-text-content figcaption {
  text-align: center;
  color: #666;
  font-size: 0.9em;
  margin-top: 0.5em;
}

/* 文字對齊樣式 */
.rich-text-content .text-left,
.rich-text-content [style*="text-align: left"] {
  text-align: left !important;
}

.rich-text-content .text-center,
.rich-text-content [style*="text-align: center"] {
  text-align: center !important;
}

.rich-text-content .text-right,
.rich-text-content [style*="text-align: right"] {
  text-align: right !important;
}

.rich-text-content .text-justify,
.rich-text-content [style*="text-align: justify"] {
  text-align: justify !important;
}

/* 支援內聯樣式 - 確保編輯器樣式在前端正確顯示 */
.rich-text-content [style*="color"]:not([style*="text-align"]) {
  /* 保留顏色樣式，但不影響對齊 */
}

.rich-text-content [style*="background-color"]:not([style*="text-align"]) {
  /* 保留背景色樣式，但不影響對齊 */
}

.rich-text-content [style*="font-size"]:not([style*="text-align"]) {
  /* 保留字體大小樣式，但不影響對齊 */
}

.rich-text-content [style*="font-family"]:not([style*="text-align"]) {
  /* 保留字體樣式，但不影響對齊 */
}

/* 重要：不要使用 inherit，讓內聯樣式自然生效 */
/* 這些選擇器存在是為了提高優先級，但不設定任何會覆蓋內聯樣式的屬性 */
.rich-text-content span[style*="color"],
.rich-text-content p[style*="color"],
.rich-text-content strong[style*="color"],
.rich-text-content em[style*="color"],
.rich-text-content b[style*="color"],
.rich-text-content h1[style*="color"],
.rich-text-content h2[style*="color"],
.rich-text-content h3[style*="color"],
.rich-text-content h4[style*="color"],
.rich-text-content h5[style*="color"],
.rich-text-content h6[style*="color"] {
  /* 不設定 color 屬性，讓內聯樣式自然生效 */
}

.rich-text-content span[style*="background-color"],
.rich-text-content p[style*="background-color"],
.rich-text-content strong[style*="background-color"],
.rich-text-content em[style*="background-color"],
.rich-text-content b[style*="background-color"],
.rich-text-content h1[style*="background-color"],
.rich-text-content h2[style*="background-color"],
.rich-text-content h3[style*="background-color"],
.rich-text-content h4[style*="background-color"],
.rich-text-content h5[style*="background-color"],
.rich-text-content h6[style*="background-color"] {
  /* 不設定 background-color 屬性，讓內聯樣式自然生效 */
}

/* 強制確保特定 HSL 顏色值能正確顯示 - 使用更高優先級 */
.rich-text-content *[style*="hsl(0, 75%, 60%)"],
.rich-text-content span[style*="hsl(0, 75%, 60%)"],
.rich-text-content strong[style*="hsl(0, 75%, 60%)"],
.rich-text-content p[style*="hsl(0, 75%, 60%)"] {
  color: hsl(0, 75%, 60%) !important;
}

.rich-text-content *[style*="hsl(180, 75%, 60%)"],
.rich-text-content span[style*="hsl(180, 75%, 60%)"],
.rich-text-content strong[style*="hsl(180, 75%, 60%)"],
.rich-text-content p[style*="hsl(180, 75%, 60%)"] {
  color: hsl(180, 75%, 60%) !important;
}

.rich-text-content *[style*="background-color:hsl(0, 75%, 60%)"],
.rich-text-content span[style*="background-color:hsl(0, 75%, 60%)"],
.rich-text-content strong[style*="background-color:hsl(0, 75%, 60%)"],
.rich-text-content p[style*="background-color:hsl(0, 75%, 60%)"] {
  background-color: hsl(0, 75%, 60%) !important;
}

.rich-text-content *[style*="background-color:hsl(180, 75%, 60%)"],
.rich-text-content span[style*="background-color:hsl(180, 75%, 60%)"],
.rich-text-content strong[style*="background-color:hsl(180, 75%, 60%)"],
.rich-text-content p[style*="background-color:hsl(180, 75%, 60%)"] {
  background-color: hsl(180, 75%, 60%) !important;
}

/* 確保所有內聯樣式都能正確顯示，不被其他CSS覆蓋 */
.rich-text-content *[style] {
  /* 保留所有內聯樣式的優先級 */
}

/* 創建一個更高優先級的選擇器來覆蓋 Tailwind 的 important 設定 */
html body .rich-text-content *[style*="color:"],
html body .highlights-content *[style*="color:"] {
  /* 不設定任何會覆蓋內聯樣式的屬性 */
}

html body .rich-text-content *[style*="background-color:"],
html body .highlights-content *[style*="background-color:"] {
  /* 不設定任何會覆蓋內聯樣式的屬性 */
}

/* 支援富文本編輯器的特殊樣式 */
.rich-text-content [style*="margin-bottom: 0px"] {
  margin-bottom: 0 !important;
}

.rich-text-content [style*="flex-shrink"] {
  display: inline-block;
}

.rich-text-content [style*="overflow-wrap: break-word"] {
  overflow-wrap: break-word !important;
}

.rich-text-content [style*="hyphens: auto"] {
  hyphens: auto !important;
}

.rich-text-content [style*="white-space-collapse: preserve"] {
  white-space: pre-wrap !important;
}

/* 支援字體粗細 */
.rich-text-content [style*="font-weight: bold"] {
  font-weight: bold !important;
}

.rich-text-content [style*="font-weight: normal"] {
  font-weight: normal !important;
}

/* 確保中文字體正確顯示 */
.rich-text-content [style*="微軟正黑體"],
.rich-text-content [style*="Microsoft JhengHei"] {
  font-family: "微軟正黑體", "Microsoft JhengHei", sans-serif !important;
}

.rich-text-content [style*="新細明體"],
.rich-text-content [style*="PMingLiU"] {
  font-family: "新細明體", "PMingLiU", serif !important;
}

.rich-text-content [style*="標楷體"],
.rich-text-content [style*="DFKai-SB"] {
  font-family: "標楷體", "DFKai-SB", serif !important;
}

.rich-text-content [style*="Arial"] {
  font-family: Arial, sans-serif !important;
}

.rich-text-content [style*="Tahoma"] {
  font-family: Tahoma, sans-serif !important;
}

.rich-text-content [style*="Times New Roman"] {
  font-family: "Times New Roman", serif !important;
}

.rich-text-content [style*="Georgia"] {
  font-family: Georgia, serif !important;
}

.rich-text-content [style*="Verdana"] {
  font-family: Verdana, sans-serif !important;
}

.rich-text-content [style*="Courier New"] {
  font-family: "Courier New", monospace !important;
}

.rich-text-content [style*="Comic Sans MS"] {
  font-family: "Comic Sans MS", cursive !important;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .rich-text-content {
    font-size: 16px;
  }
  
  .rich-text-content .image-style-align-left,
  .rich-text-content .image-style-align-right,
  .rich-text-content .image-style-side {
    float: none;
    margin: 1em auto;
    display: table;
  }
  
  .rich-text-content table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}

/* 清除浮動 */
.rich-text-content::after {
  content: "";
  display: table;
  clear: both;
}

/* 確保段落內的圖片也能正確對齊 */
.rich-text-content p img {
  display: inline-block;
  vertical-align: middle;
}

.rich-text-content p[style*="text-align: center"] img {
  display: block !important;
  margin: 0 auto !important;
}

/* 支援更多 CKEditor 類名格式 */
.rich-text-content .ck-align-center,
.rich-text-content .ck-widget_selected {
  text-align: center !important;
}

.rich-text-content .ck-align-center img,
.rich-text-content .ck-widget_selected img {
  margin: 0 auto !important;
  display: block !important;
}