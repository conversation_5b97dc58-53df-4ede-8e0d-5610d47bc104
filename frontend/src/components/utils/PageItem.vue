<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  totalPage: {
    type: Number,
    required: true,
  },
  initialPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['pageChange', 'pageSizeChange'])

const nowPage = ref(props.initialPage)
const currentPageSize = ref(props.pageSize)

// 每頁顯示數量選項
const pageSizeOptions = [10, 20, 50, 100]

// 檢測是否為手機裝置
const isMobile = ref(false)

// 計算顯示的頁碼數字
const pageNumbers = computed(() => {
  const totalPages = Math.max(1, Math.ceil(props.totalPage / currentPageSize.value))
  const current = nowPage.value

  // 根據螢幕大小決定顯示的頁碼數量
  const maxPages = isMobile.value ? 5 : 9

  if (totalPages <= maxPages) {
    // 如果總頁數小於等於最大顯示數，顯示所有頁碼
    return Array.from({ length: totalPages }, (_, i) => i + 1)
  }

  // 總頁數大於最大顯示數時
  const halfMax = Math.floor(maxPages / 2)

  if (current <= halfMax + 1) {
    return Array.from({ length: maxPages }, (_, i) => i + 1)
  }

  if (current >= totalPages - halfMax) {
    return Array.from({ length: maxPages }, (_, i) => totalPages - maxPages + 1 + i)
  }

  return Array.from({ length: maxPages }, (_, i) => current - halfMax + i)
})

// 檢測螢幕大小
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 640
}

// 監聽視窗大小變化
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 計算總頁數
const totalPages = computed(() => Math.max(1, Math.ceil(props.totalPage / currentPageSize.value)))

// 切換頁面
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === nowPage.value || props.loading) return
  
  nowPage.value = page
  emit('pageChange', page)
}

// 首頁
const goToFirstPage = () => {
  if (nowPage.value !== 1 && !props.loading) {
    changePage(1)
  }
}

// 末頁
const goToLastPage = () => {
  if (nowPage.value !== totalPages.value && !props.loading) {
    changePage(totalPages.value)
  }
}

// 上一頁
const prevPage = () => {
  if (nowPage.value > 1 && !props.loading) {
    changePage(nowPage.value - 1)
  }
}

// 下一頁
const nextPage = () => {
  if (nowPage.value < totalPages.value && !props.loading) {
    changePage(nowPage.value + 1)
  }
}

// 改變每頁顯示數量
const changePageSize = (newPageSize) => {
  if (props.loading) return
  
  currentPageSize.value = newPageSize
  nowPage.value = 1 // 重置到第一頁
  emit('pageSizeChange', newPageSize)
  emit('pageChange', 1)
}

// 監聽總頁數變化，如果當前頁超出範圍則重置
watch(() => props.totalPage, (newTotal) => {
  const newTotalPages = Math.max(1, Math.ceil(newTotal / currentPageSize.value))
  if (nowPage.value > newTotalPages) {
    nowPage.value = 1
    emit('pageChange', 1)
  }
})

// 監聽pageSize prop變化
watch(() => props.pageSize, (newPageSize) => {
  if (newPageSize !== currentPageSize.value) {
    currentPageSize.value = newPageSize
  }
})

// 組件掛載時設置初始頁面
onMounted(() => {
  if (props.initialPage !== nowPage.value) {
    nowPage.value = props.initialPage
  }
  if (props.pageSize !== currentPageSize.value) {
    currentPageSize.value = props.pageSize
  }
})

// 暴露當前頁面給父組件
defineExpose({
  nowPage,
  changePage,
  currentPageSize,
})
</script>

<template>
  <div class="pagination-wrapper">
    <!-- 分頁控制器和每頁顯示數量選擇器在同一行 -->
    <div class="pagination-main-container flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4" :class="{ 'loading': loading }">
      <!-- 分頁控制器 -->
      <div class="pagination-container flex justify-center lg:justify-start items-center space-x-1">
        <!-- 首頁按鈕 -->
        <button
          @click="goToFirstPage"
          :disabled="nowPage === 1 || loading"
          class="pagination-btn first-btn"
          :class="{ 'disabled': nowPage === 1 || loading }"
          title="首頁"
        >
          <i class="fas fa-angle-double-left"></i>
          <span class="hidden sm:inline ml-1">首頁</span>
        </button>

        <!-- 上一頁按鈕 -->
        <button
          @click="prevPage"
          :disabled="nowPage === 1 || loading"
          class="pagination-btn prev-btn"
          :class="{ 'disabled': nowPage === 1 || loading }"
          title="上一頁"
        >
          <i class="fas fa-chevron-left"></i>
          <span class="hidden sm:inline ml-1">上一頁</span>
        </button>

        <!-- 頁碼按鈕 -->
        <div class="page-numbers flex space-x-1">
          <!-- 顯示省略號（如果需要） -->
          <span v-if="pageNumbers[0] > 1" class="pagination-ellipsis">...</span>
          
          <button
            v-for="page in pageNumbers"
            :key="page"
            @click="changePage(page)"
            :disabled="loading"
            class="page-number-btn"
            :class="{ 'active': page === nowPage, 'disabled': loading }"
          >
            {{ page }}
          </button>
          
          <!-- 顯示省略號（如果需要） -->
          <span v-if="pageNumbers[pageNumbers.length - 1] < totalPages" class="pagination-ellipsis">...</span>
        </div>

        <!-- 下一頁按鈕 -->
        <button
          @click="nextPage"
          :disabled="nowPage === totalPages || loading"
          class="pagination-btn next-btn"
          :class="{ 'disabled': nowPage === totalPages || loading }"
          title="下一頁"
        >
          <span class="hidden sm:inline mr-1">下一頁</span>
          <i class="fas fa-chevron-right"></i>
        </button>

        <!-- 末頁按鈕 -->
        <button
          @click="goToLastPage"
          :disabled="nowPage === totalPages || loading"
          class="pagination-btn last-btn"
          :class="{ 'disabled': nowPage === totalPages || loading }"
          title="末頁"
        >
          <span class="hidden sm:inline mr-1">末頁</span>
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>

      <!-- 每頁顯示數量選擇器 -->
      <div class="page-size-selector flex items-center justify-center lg:justify-end">
        <span class="text-sm text-gray-600 mr-2 whitespace-nowrap">每頁顯示：</span>
        <select 
          v-model="currentPageSize" 
          @change="changePageSize(currentPageSize)"
          :disabled="loading"
          class="page-size-select"
          :class="{ 'disabled': loading }"
        >
          <option v-for="size in pageSizeOptions" :key="size" :value="size">
            {{ size }} 項
          </option>
        </select>
        
        <!-- 載入指示器 -->
        <div v-if="loading" class="ml-3">
          <div class="inline-block w-4 h-4 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
        </div>
      </div>
    </div>

    <!-- 分頁資訊 -->
    <div class="pagination-info text-center text-sm text-gray-500 mt-4">
      第 {{ nowPage }} 頁，共 {{ totalPages }} 頁，總計 {{ totalPage }} 項
      <span class="ml-2">
        （顯示第 {{ ((nowPage - 1) * currentPageSize + 1) }} - 
        {{ Math.min(nowPage * currentPageSize, totalPage) }} 項）
      </span>
      <span v-if="loading" class="ml-2 text-blue-600">載入中...</span>
    </div>
  </div>
</template>

<style scoped>
.pagination-wrapper {
  margin: 2rem 0;
}

.pagination-main-container {
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-size-select {
  @apply px-3 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-20;
}

.pagination-container {
  gap: 0.25rem;
}

.pagination-btn {
  @apply px-3 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 2.5rem;
  height: 2.5rem;
  justify-content: center;
}

.pagination-btn.disabled {
  @apply opacity-50 cursor-not-allowed bg-gray-100;
}

.pagination-btn.disabled:hover {
  @apply bg-gray-100;
}

.page-number-btn {
  @apply w-10 h-10 flex items-center justify-center bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm font-medium;
}

.page-number-btn.active {
  @apply bg-blue-600 text-white border-blue-600;
}

.page-number-btn.active:hover {
  @apply bg-blue-700;
}

.pagination-ellipsis {
  @apply w-10 h-10 flex items-center justify-center text-gray-400;
}

.pagination-info {
  font-size: 0.875rem;
  line-height: 1.5;
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

/* 響應式設計 */
@media (max-width: 1024px) {
  .pagination-main-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .pagination-container {
    justify-content: center;
  }
  
  .page-size-selector {
    justify-content: center;
  }
}

@media (max-width: 640px) {
  .pagination-main-container {
    padding: 0.75rem;
  }

  .pagination-container {
    flex-wrap: nowrap !important; /* 強制防止換行 */
    gap: 0.125rem; /* 減少間距 */
    overflow-x: auto; /* 允許水平滾動 */
    -webkit-overflow-scrolling: touch; /* iOS 平滑滾動 */
    scrollbar-width: none; /* Firefox 隱藏滾動條 */
    -ms-overflow-style: none; /* IE 隱藏滾動條 */
    /* 覆蓋 space-x-1 */
    & > * + * {
      margin-left: 0.125rem !important;
    }
  }

  .pagination-container::-webkit-scrollbar {
    display: none; /* Chrome/Safari 隱藏滾動條 */
  }

  .pagination-btn {
    @apply px-2 py-1 text-xs min-w-8 h-8;
    flex-shrink: 0; /* 防止按鈕被壓縮 */
    white-space: nowrap; /* 防止文字換行 */
  }

  .page-number-btn {
    @apply w-8 h-8 text-xs;
    flex-shrink: 0; /* 防止按鈕被壓縮 */
    min-width: 2rem; /* 確保最小寬度 */
  }

  .page-numbers {
    display: flex;
    gap: 0.125rem;
    flex-shrink: 0; /* 防止頁碼區域被壓縮 */
  }

  .pagination-ellipsis {
    flex-shrink: 0; /* 防止省略號被壓縮 */
  }

  /* 在手機版隱藏每頁顯示數量選擇器 */
  .page-size-selector {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .pagination-btn span {
    display: none !important; /* 強制隱藏按鈕文字 */
  }

  .pagination-btn {
    min-width: 1.75rem;
    height: 1.75rem;
    padding: 0.25rem;
    flex-shrink: 0; /* 防止按鈕被壓縮 */
  }

  .page-number-btn {
    @apply w-7 h-7 text-xs;
    min-width: 1.75rem;
    flex-shrink: 0; /* 防止按鈕被壓縮 */
  }

  .pagination-info {
    font-size: 0.75rem;
    padding: 0.5rem;
  }

  /* 在極小螢幕上進一步優化 */
  .pagination-container {
    gap: 0.0625rem !important; /* 更小的間距 */
    /* 覆蓋 space-x-1 */
    & > * + * {
      margin-left: 0.0625rem !important;
    }
  }

  .page-numbers {
    gap: 0.0625rem;
  }
}

.pagination-main-container.loading {
  opacity: 0.8;
  position: relative;
}

.page-size-select.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.page-number-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 載入動畫 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 