<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>富文本顏色測試頁面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 5px;
        }
        
        .rich-text-content {
            line-height: 1.8;
            color: #333;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        .highlights-content {
            text-align: left;
            line-height: 1.6;
        }
        
        .expected {
            margin-top: 15px;
            padding: 10px;
            background-color: #f0f8ff;
            border-left: 4px solid #007acc;
            font-size: 14px;
            color: #666;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #007acc;
            color: #007acc;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>富文本顏色顯示測試</h1>
        <p>這個頁面用於測試富文本編輯器的顏色設定是否能在前台正確顯示。</p>
        
        <!-- 重點商品資訊測試 -->
        <div class="test-section">
            <div class="test-title">重點商品資訊 (highlights)</div>
            <div class="rich-text-content highlights-content">
                <p style="margin-left:0px;">
                    <span style="background-color:hsl(0, 75%, 60%);color:hsl(180, 75%, 60%);">
                        <strong>▲ 強力上市</strong>
                    </span>
                </p>
                <p style="margin-left:0px;">
                    <span style="color:hsl(120, 75%, 60%);">
                        <strong>⭐ 安地斯山脈 優質10倍濃縮馬卡</strong>
                    </span>
                </p>
                <p style="margin-left:0px;">
                    <span style="color:hsl(240, 75%, 60%);">
                        <strong>⭐ enXtra 專利南薑萃取物</strong>
                    </span>
                </p>
            </div>
            <div class="expected">
                預期效果：第一行應該是紅色背景青色文字，第二行是綠色文字，第三行是藍色文字
            </div>
        </div>
        
        <!-- 標籤切換測試 -->
        <div class="test-section">
            <div class="test-title">標籤切換測試</div>
            <div class="tabs">
                <div class="tab active" onclick="switchTab('description')">商品說明</div>
                <div class="tab" onclick="switchTab('specs')">商品規格</div>
            </div>
            
            <div id="description" class="tab-content active">
                <div class="rich-text-content">
                    <h1>商品說明</h1>
                    <p style="margin-left:0px;">
                        <span style="background-color:hsl(0, 75%, 60%);color:hsl(180, 75%, 60%);">
                            <strong>正宗秘魯國寶 彩紅馬卡MACA</strong>
                        </span>
                    </p>
                    <p>這是一個測試商品，用於驗證富文本編輯器的顏色顯示功能。</p>
                    <p>
                        <span style="color:hsl(120, 75%, 60%);">綠色文字測試</span> - 
                        <span style="color:hsl(240, 75%, 60%);">藍色文字測試</span> - 
                        <span style="color:rgb(255, 0, 0);">紅色文字測試</span>
                    </p>
                    <p>
                        <span style="background-color:hsl(120, 75%, 60%);color:#ffffff;">綠色背景白色文字</span>
                    </p>
                </div>
            </div>
            
            <div id="specs" class="tab-content">
                <div class="rich-text-content">
                    <h2>商品規格</h2>
                    <p>
                        <span style="color:rgb(255, 0, 0);">重要規格資訊</span>
                    </p>
                    <ul>
                        <li><span style="color:hsl(194, 70%, 21%);">容量：500ml</span></li>
                        <li><span style="color:hsl(216, 5%, 32%);">重量：1kg</span></li>
                        <li><span style="background-color:hsl(240, 75%, 60%);color:#ffffff;">特殊規格：藍色背景白色文字</span></li>
                    </ul>
                </div>
            </div>
            
            <div class="expected">
                預期效果：切換標籤時，所有顏色設定都應該正確顯示
            </div>
        </div>
        
        <!-- RGB 顏色測試 -->
        <div class="test-section">
            <div class="test-title">RGB 顏色測試</div>
            <div class="rich-text-content">
                <p>
                    <span style="color:rgb(255, 0, 0);">純紅色文字 (RGB)</span> - 
                    <span style="color:rgb(0, 255, 0);">純綠色文字 (RGB)</span> - 
                    <span style="color:rgb(0, 0, 255);">純藍色文字 (RGB)</span>
                </p>
                <p>
                    <span style="background-color:rgb(255, 0, 0);color:#ffffff;">紅色背景白色文字</span> - 
                    <span style="background-color:rgb(0, 255, 0);color:#000000;">綠色背景黑色文字</span> - 
                    <span style="background-color:rgb(0, 0, 255);color:#ffffff;">藍色背景白色文字</span>
                </p>
            </div>
            <div class="expected">
                預期效果：所有 RGB 顏色都應該正確顯示
            </div>
        </div>
    </div>
    
    <script>
        function switchTab(tabName) {
            // 移除所有 active 類別
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // 添加 active 類別到選中的標籤
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
            
            // 模擬 Vue 的 forceInlineStyles 函數
            setTimeout(() => {
                forceInlineStyles();
            }, 50);
        }
        
        function forceInlineStyles() {
            const richTextContainers = document.querySelectorAll('.rich-text-content');
            richTextContainers.forEach(container => {
                const elementsWithStyle = container.querySelectorAll('*[style]');
                elementsWithStyle.forEach(element => {
                    const style = element.getAttribute('style');
                    if (!style) return;

                    const styleProps = style.split(';').filter(prop => prop.trim());
                    styleProps.forEach(prop => {
                        const [property, value] = prop.split(':').map(s => s.trim());
                        if (property && value) {
                            element.style.setProperty(property, value, 'important');
                        }
                    });
                });
            });
            console.log('✅ 已應用內聯樣式修正');
        }
        
        // 頁面載入時執行一次
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                forceInlineStyles();
            }, 100);
        });
    </script>
</body>
</html>
