0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@10.9.2
2 info using node@v22.17.0
3 silly config load:file:/usr/local/lib/node_modules/npm/npmrc
4 silly config load:file:/Users/<USER>/Desktop/culroc---main-main/.npmrc
5 silly config load:file:/Users/<USER>/.npmrc
6 silly config load:file:/usr/local/etc/npmrc
7 verbose title npm run dev:frontend
8 verbose argv "run" "dev:frontend"
9 verbose logfile logs-max:10 dir:/Users/<USER>/Desktop/culroc---main-main/.npm-cache/_logs/2025-07-18T03_49_05_381Z-
10 verbose logfile /Users/<USER>/Desktop/culroc---main-main/.npm-cache/_logs/2025-07-18T03_49_05_381Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
