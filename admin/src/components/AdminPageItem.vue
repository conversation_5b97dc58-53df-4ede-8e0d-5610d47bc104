<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  total: {
    type: Number,
    required: true,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showSizeChanger: {
    type: Boolean,
    default: true,
  },
  showQuickJumper: {
    type: Boolean,
    default: true,
  },
  showTotal: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['pageChange', 'pageSizeChange'])

const nowPage = ref(props.currentPage)
const currentPageSize = ref(props.pageSize)
const jumpToPage = ref('')

// 每頁顯示數量選項
const pageSizeOptions = [10, 20, 50, 100]

// 計算總頁數
const totalPages = computed(() => Math.max(1, Math.ceil(props.total / currentPageSize.value)))

// 檢測是否為手機裝置
const isMobile = ref(false)

// 計算顯示的頁碼數字（簡化版本，適合後台）
const pageNumbers = computed(() => {
  const total = totalPages.value
  const current = nowPage.value

  // 根據螢幕大小決定顯示的頁碼數量
  const maxPages = isMobile.value ? 5 : 7

  if (total <= maxPages) {
    // 如果總頁數小於等於最大顯示數，顯示所有頁碼
    return Array.from({ length: total }, (_, i) => i + 1)
  }

  // 總頁數大於最大顯示數時
  const halfMax = Math.floor(maxPages / 2)

  if (current <= halfMax + 1) {
    return Array.from({ length: maxPages }, (_, i) => i + 1)
  }

  if (current >= total - halfMax) {
    return Array.from({ length: maxPages }, (_, i) => total - maxPages + 1 + i)
  }

  return Array.from({ length: maxPages }, (_, i) => current - halfMax + i)
})

// 檢測螢幕大小
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 640
}

// 計算顯示範圍
const displayRange = computed(() => {
  const start = (nowPage.value - 1) * currentPageSize.value + 1
  const end = Math.min(nowPage.value * currentPageSize.value, props.total)
  return { start, end }
})

// 切換頁面
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === nowPage.value || props.loading) return
  
  nowPage.value = page
  emit('pageChange', page)
}

// 首頁
const goToFirstPage = () => {
  if (nowPage.value !== 1 && !props.loading) {
    changePage(1)
  }
}

// 末頁
const goToLastPage = () => {
  if (nowPage.value !== totalPages.value && !props.loading) {
    changePage(totalPages.value)
  }
}

// 上一頁
const prevPage = () => {
  if (nowPage.value > 1 && !props.loading) {
    changePage(nowPage.value - 1)
  }
}

// 下一頁
const nextPage = () => {
  if (nowPage.value < totalPages.value && !props.loading) {
    changePage(nowPage.value + 1)
  }
}

// 改變每頁顯示數量
const changePageSize = (newPageSize) => {
  if (props.loading) return
  
  currentPageSize.value = newPageSize
  nowPage.value = 1 // 重置到第一頁
  emit('pageSizeChange', newPageSize)
  emit('pageChange', 1)
}

// 快速跳轉
const handleQuickJump = () => {
  const pageNum = parseInt(jumpToPage.value)
  if (pageNum && pageNum >= 1 && pageNum <= totalPages.value) {
    changePage(pageNum)
    jumpToPage.value = ''
  }
}

// 監聽總數變化，如果當前頁超出範圍則重置
watch(() => props.total, (newTotal) => {
  const newTotalPages = Math.max(1, Math.ceil(newTotal / currentPageSize.value))
  if (nowPage.value > newTotalPages) {
    nowPage.value = 1
    emit('pageChange', 1)
  }
})

// 監聽外部currentPage變化
watch(() => props.currentPage, (newPage) => {
  if (newPage !== nowPage.value) {
    nowPage.value = newPage
  }
})

// 監聽pageSize prop變化
watch(() => props.pageSize, (newPageSize) => {
  if (newPageSize !== currentPageSize.value) {
    currentPageSize.value = newPageSize
  }
})

// 組件掛載時設置初始狀態
onMounted(() => {
  nowPage.value = props.currentPage
  currentPageSize.value = props.pageSize
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

// 組件卸載時清理事件監聽器
onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 暴露當前狀態給父組件
defineExpose({
  nowPage,
  changePage,
  currentPageSize,
})
</script>

<template>
  <div class="admin-pagination">
    <!-- 總數顯示 -->
    <div v-if="showTotal" class="pagination-info mb-4">
      <span class="text-sm text-gray-600">
        顯示第 {{ displayRange.start }} - {{ displayRange.end }} 項，共 {{ total }} 項
      </span>
    </div>

    <!-- 分頁控制器 -->
    <div class="pagination-controls flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <!-- 左側：每頁數量選擇器 -->
      <div v-if="showSizeChanger" class="flex items-center space-x-2">
        <span class="text-sm text-gray-600">每頁顯示</span>
        <select
          v-model="currentPageSize"
          @change="changePageSize(currentPageSize)"
          :disabled="loading"
          class="form-select text-sm w-20"
        >
          <option v-for="size in pageSizeOptions" :key="size" :value="size">
            {{ size }}
          </option>
        </select>
        <span class="text-sm text-gray-600">項</span>
      </div>

      <!-- 中間：頁碼按鈕 -->
      <div class="pagination-buttons flex items-center space-x-1">
        <!-- 首頁 -->
        <button
          @click="goToFirstPage"
          :disabled="nowPage === 1 || loading"
          class="pagination-btn"
          :class="{ 'disabled': nowPage === 1 || loading }"
          title="首頁"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>

        <!-- 上一頁 -->
        <button
          @click="prevPage"
          :disabled="nowPage === 1 || loading"
          class="pagination-btn"
          :class="{ 'disabled': nowPage === 1 || loading }"
          title="上一頁"
        >
          <i class="fas fa-chevron-left"></i>
        </button>

        <!-- 省略號（前） -->
        <span v-if="pageNumbers[0] > 1" class="pagination-ellipsis">...</span>

        <!-- 頁碼 -->
        <button
          v-for="page in pageNumbers"
          :key="page"
          @click="changePage(page)"
          :disabled="loading"
          class="page-number-btn"
          :class="{ 'active': page === nowPage, 'disabled': loading }"
        >
          {{ page }}
        </button>

        <!-- 省略號（後） -->
        <span v-if="pageNumbers[pageNumbers.length - 1] < totalPages" class="pagination-ellipsis">...</span>

        <!-- 下一頁 -->
        <button
          @click="nextPage"
          :disabled="nowPage === totalPages || loading"
          class="pagination-btn"
          :class="{ 'disabled': nowPage === totalPages || loading }"
          title="下一頁"
        >
          <i class="fas fa-chevron-right"></i>
        </button>

        <!-- 末頁 -->
        <button
          @click="goToLastPage"
          :disabled="nowPage === totalPages || loading"
          class="pagination-btn"
          :class="{ 'disabled': nowPage === totalPages || loading }"
          title="末頁"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>

      <!-- 右側：快速跳轉 -->
      <div v-if="showQuickJumper" class="flex items-center space-x-2">
        <span class="text-sm text-gray-600">跳至</span>
        <input
          v-model="jumpToPage"
          @keyup.enter="handleQuickJump"
          :disabled="loading"
          type="number"
          min="1"
          :max="totalPages"
          placeholder="頁碼"
          class="form-input text-sm w-16"
        />
        <span class="text-sm text-gray-600">頁</span>
        <button
          @click="handleQuickJump"
          :disabled="loading"
          class="btn-blue-sm"
        >
          跳轉
        </button>
      </div>
    </div>

    <!-- 載入狀態遮罩 -->
    <div v-if="loading" class="pagination-loading-overlay">
      <div class="flex items-center justify-center">
        <div class="spinner mr-2"></div>
        <span class="text-sm text-gray-600">載入中...</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.admin-pagination {
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
  @apply w-8 h-8 flex items-center justify-center border border-gray-300 rounded bg-white text-gray-600 hover:bg-gray-50 hover:border-blue-500 transition-colors;
}

.pagination-btn:disabled,
.pagination-btn.disabled {
  @apply bg-gray-100 text-gray-400 cursor-not-allowed hover:bg-gray-100 hover:border-gray-300;
}

.page-number-btn {
  @apply w-8 h-8 flex items-center justify-center border border-gray-300 rounded bg-white text-gray-700 hover:bg-blue-50 hover:border-blue-500 transition-colors;
}

.page-number-btn.active {
  @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
}

.page-number-btn:disabled,
.page-number-btn.disabled {
  @apply bg-gray-100 text-gray-400 cursor-not-allowed hover:bg-gray-100 hover:border-gray-300;
}

.pagination-ellipsis {
  @apply px-2 text-gray-400;
}

.form-select,
.form-input {
  @apply border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.btn-blue-sm {
  @apply bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium py-1 px-2 rounded;
}

.spinner {
  @apply w-4 h-4 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin;
}

.pagination-loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded;
}

/* 響應式設計 */
@media (max-width: 640px) {
  .pagination-controls {
    @apply flex-col space-y-2;
  }

  .pagination-buttons {
    @apply justify-center;
    flex-wrap: nowrap !important; /* 強制防止換行 */
    overflow-x: auto; /* 允許水平滾動 */
    -webkit-overflow-scrolling: touch; /* iOS 平滑滾動 */
    scrollbar-width: none; /* Firefox 隱藏滾動條 */
    -ms-overflow-style: none; /* IE 隱藏滾動條 */
    gap: 0.125rem; /* 減少間距 */
    /* 覆蓋可能的間距 */
    & > * + * {
      margin-left: 0.125rem !important;
    }
  }

  .pagination-buttons::-webkit-scrollbar {
    display: none; /* Chrome/Safari 隱藏滾動條 */
  }

  .page-number-btn,
  .pagination-btn,
  .pagination-ellipsis {
    @apply w-7 h-7 text-xs;
    flex-shrink: 0 !important; /* 強制防止按鈕被壓縮 */
    min-width: 1.75rem; /* 確保最小寬度 */
    white-space: nowrap; /* 防止文字換行 */
  }

  /* 在手機版隱藏每頁顯示數量選擇器 */
  .pagination-controls > div:first-child {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .page-number-btn,
  .pagination-btn {
    @apply w-6 h-6 text-xs;
    min-width: 1.5rem;
  }

  .pagination-buttons {
    gap: 0.0625rem !important; /* 更小的間距 */
    /* 覆蓋可能的間距 */
    & > * + * {
      margin-left: 0.0625rem !important;
    }
  }
}
</style> 